package repository

import (
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type dueNotiConfigExcludedBuyerRepositoryImpl struct {
	DB *gorm.DB
}

type DueNotiConfigExcludedBuyerRepository interface {
	GetTopExcludedBuyersByNotificationIds(ids []int, topN int) (map[int][]entity.DueNotiConfigExcludedBuyer, error)
	GetExcludedBuyersByNotificationId(id int) ([]entity.DueNotiConfigExcludedBuyer, error)

	BulkInsertExcludedBuyers(excludedBuyers []entity.DueNotiConfigExcludedBuyer) error
	PermanentDeleteExcludedBuyers(dueNotiConfigId int) error

	DeleteExcludedBuyerNotificationId(dueNotiConfigId int) error
	GetDB() *gorm.DB
}

func NewDueNotiConfigExcludedBuyerRepository(db *gorm.DB) DueNotiConfigExcludedBuyerRepository {
	return &dueNotiConfigExcludedBuyerRepositoryImpl{DB: db}
}
