package dto

import (
	"backend-common-lib/model"
)

type MemberSearchReqDto struct {
	CustomerGroupId      *int    `json:"customerGroupId"`
	Username             *string `json:"username"`
	BidderId             *string `json:"bidderId"`
	FullName             *string `json:"fullName"`
	IdentificationNumber *string `json:"identificationNumber"`
	model.PagingRequest
}

type MemberSearchRespDto struct {
	Id                   int     `json:"id"`
	Status               bool    `json:"status"`
	BidderId             *string `json:"bidderId"`
	BidderNumber         *string `json:"bidderNumber"`
	Username             *string `json:"username"`
	IdentificationNumber *string `json:"identificationNumber"`
	PrefixNameTh         *string `json:"prefixNameTh"`
	PrefixNameEn         *string `json:"prefixNameEn"`
	FullName             *string `json:"fullName"`
	CustomerGroupDesc    *string `json:"customerGroupDesc"`
	IsActive             bool    `json:"isActive"`
	RegisChannel         *string `json:"regisChannel"`
	PhoneNumber          *string `json:"phoneNumber"`
	IsBlock              bool    `json:"isBlock"`
	IsBlacklist          bool    `json:"isBlacklist"`
	AccountStatusTh      *string `json:"accountStatusTh"`
	AccountStatusEn      *string `json:"accountStatusEn"`
	UserStatusTh         *string `json:"userStatusTh"`
	UserStatusEn         *string `json:"userStatusEn"`
}

type MemberPageRespDto[T any] struct {
	model.PagingModel[T]
}

type UpdateMemberStatusReqDto struct {
	model.BaseDtoActionBy
	IsBlock     *bool   `json:"isBlock"`
	IsBlacklist *bool   `json:"isBlacklist"`
	Reason      *string `json:"reason"`
	IsActive    *bool   `json:"isActive"`
}
