package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (r *masterAssetLocationRepositoryImpl) buildMasterAssetLocationQuery(req dto.MasterAssetLocationPageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.MasterAssetLocation{})
	query = util.JoinUsers("master_asset_location")(query)
	query = util.ApplyFiltersFromStruct(query, req)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_asset_location")
	}
	return query
}

func (r *masterAssetLocationRepositoryImpl) FindMasterAssetLocationWithFilter(req dto.MasterAssetLocationPageReqDto) ([]entity.MasterAssetLocation, error) {
	var results []entity.MasterAssetLocation

	query := r.buildMasterAssetLocationQuery(req)
	query.Order("location_code asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *masterAssetLocationRepositoryImpl) FindById(id int) (*entity.MasterAssetLocation, error) {
	var result entity.MasterAssetLocation
	if err := r.DB.First(&result, id).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (r *masterAssetLocationRepositoryImpl) CountMasterAssetLocationWithFilter(req dto.MasterAssetLocationPageReqDto) (int64, error) {
	var count int64
	query := r.buildMasterAssetLocationQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *masterAssetLocationRepositoryImpl) FindMasterAssetLocationLatestSyncDate() (*time.Time, error) {
	var result entity.MasterAssetLocation
	err := r.DB.
		Model(&entity.MasterAssetLocation{}).
		Select("latest_sync_date").
		Where("latest_sync_date IS NOT NULL").
		Order("latest_sync_date desc").
		Limit(1).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return result.LatestSyncDate, nil
}

func (r *masterAssetLocationRepositoryImpl) UpdatesMasterAssetLocationFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.MasterAssetLocation{}).Where(whereClause, args...).Updates(fields)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *masterAssetLocationRepositoryImpl) FindMasterAssetLocationAll() ([]entity.MasterAssetLocation, error) {
	var result []entity.MasterAssetLocation
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterAssetLocationRepositoryImpl) UpdateMasterAssetLocationAllFields(e *entity.MasterAssetLocation) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterAssetLocationRepositoryImpl) InsertMasterAssetLocationList(data []entity.MasterAssetLocation) error {
	if err := r.DB.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterAssetLocationRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
