package entity

import (
	"backend-common-lib/model"

	"gorm.io/gorm"
)

type NotificationConfig struct {
	*model.BaseEntity
	UserType         *string         `column:"user_type" json:"userType"`
	NotiType         *string         `column:"noti_type" json:"notiType"`
	SystemTemplateTh *string         `column:"system_template_th" json:"systemTemplateTh"`
	SmsTemplateTh    *string         `column:"sms_template_th" json:"smsTemplateTh"`
	EmailTemplateTh  *string         `column:"email_template_th" json:"emailTemplateTh"`
	SystemTemplateEn *string         `column:"system_template_en" json:"systemTemplateEn"`
	SmsTemplateEn    *string         `column:"sms_template_en" json:"smsTemplateEn"`
	EmailTemplateEn  *string         `column:"email_template_en" json:"emailTemplateEn"`
	IsSystemNoti     bool            `column:"is_system_noti" json:"isSystemNoti"`
	IsSmsNoti        bool            `column:"is_sms_noti" json:"isSmsNoti"`
	IsEmailNoti      bool            `column:"is_email_noti" json:"isEmailNoti"`
	DeletedDate      *gorm.DeletedAt `gorm:"column:deleted_date" json:"deletedDate"`
}

func (NotificationConfig) TableName() string {
	return "notification_config"
}
