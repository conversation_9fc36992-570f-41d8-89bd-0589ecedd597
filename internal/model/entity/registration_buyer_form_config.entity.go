package entity

import (
	"backend-common-lib/model"
)

type RegistrationBuyerFormConfig struct {
	*model.BaseEntity
	IsEmailRequire   bool                   `column:"is_email_require" json:"isEmailRequire"`
	IsShowFinanceTab bool                   `column:"is_show_finance_tab" json:"isShowFinanceTab"`
	CreatedUser      *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:UserID"`
	UpdatedUser      *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:UserID"`
}

func (RegistrationBuyerFormConfig) TableName() string {
	return "registration_buyer_form_config"
}
