package repository

import (
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type buyerEditProfileRequestPastRepositoryImpl struct {
	DB *gorm.DB
}

type BuyerEditProfileRequestPastRepository interface {
	GetByBuyerEditProfileRequestCurrrentId(buyerEditProfileRequestCurrentId int) (entity.BuyerEditProfileRequestPast, error)
	Insert(entity entity.BuyerEditProfileRequestPast) error

	GetDB() *gorm.DB
}

func NewBuyeEditProfileRequestPastRepository(db *gorm.DB) BuyerEditProfileRequestPastRepository {
	return &buyerEditProfileRequestPastRepositoryImpl{DB: db}
}
