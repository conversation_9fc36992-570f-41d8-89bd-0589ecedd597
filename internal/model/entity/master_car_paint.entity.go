package entity

import (
	"time"
)

type MasterCarPaint struct {
	Id            int        `gorm:"primaryKey" column:"id" json:"id" ignore:"true"`
	CompanyCode   *string    `column:"company_code" json:"cityCode"`
	CarPaintCode  *string    `column:"car_paint_code" json:"regionCode"`
	DescriptionTh *string    `column:"description_th" json:"descriptionTh"`
	DescriptionEn *string    `column:"description_en" json:"descriptionEn"`
	IsActive      bool       `column:"is_active" json:"isActive"`
	CreatedDate   time.Time  `column:"created_date" json:"createdDate" example:"2020-01-01" ignore:"true"`
	UpdatedDate   *time.Time `column:"updated_date" json:"updatedDate" example:"2020-01-01" ignore:"true"`
}

func (MasterCarPaint) TableName() string {
	return "master_car_paint"
}
