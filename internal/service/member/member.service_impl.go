package service

import (
	"backend-common-lib/constant"
	commonConstants "backend-common-lib/constants/buyer_registration_request"
	"backend-common-lib/errs"
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"math"
	"net/http"
	"strings"

	"github.com/gofiber/fiber/v2/log"
)

func (s *memberService) SearchMemberWithFilter(req dto.MemberSearchReqDto) (dto.MemberPageRespDto[dto.MemberSearchRespDto], error) {

	buyerDbs, err := s.BuyerRepo.FindBuyerWithFilter(req)
	if err != nil {
		log.Error(err)
		return dto.MemberPageRespDto[dto.MemberSearchRespDto]{}, err
	}

	count, err := s.BuyerRepo.CountBuyerWithFilter(req)
	if err != nil {
		return dto.MemberPageRespDto[dto.MemberSearchRespDto]{}, err
	}

	mapResult := make([]dto.MemberSearchRespDto, len(buyerDbs))
	for i, v := range buyerDbs {
		mapResult[i] = *util.MapToPtr[dto.MemberSearchRespDto](v)
		mapResult[i].AccountStatusTh, mapResult[i].AccountStatusEn = getAccountStatus(util.Val(v.AccountStatusCode))
		mapResult[i].UserStatusTh, mapResult[i].UserStatusEn = getUserStatus(util.Val(v.UserStatusCode))

		// map condition status active/inactive
		mapResult[i].IsActive = util.Val(v.UserIsActive)

	}

	responseDtos := dto.MemberPageRespDto[dto.MemberSearchRespDto]{
		PagingModel: *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
	}

	return responseDtos, nil
}

func getAccountStatus(code string) (th, en *string) {
	if val, ok := commonConstants.AccountStatusMap[strings.ToUpper(code)]; ok {
		return util.Ptr(val.Th), util.Ptr(val.En)
	}
	return nil, nil
}

func getUserStatus(code string) (th, en *string) {
	if val, ok := commonConstants.UserStatusMap[strings.ToUpper(code)]; ok {
		return util.Ptr(val.Th), util.Ptr(val.En)
	}
	return nil, nil
}

func (s *memberService) UpdateMemberStatus(req dto.UpdateMemberStatusReqDto) error {
	buyerDb, err := s.BuyerRepo.GetById(req.Id)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	fieldsToUpdate := map[string]interface{}{}

	if req.IsBlock != nil {
		fieldsToUpdate = map[string]interface{}{
			"is_block":               req.IsBlock,
			"reason_block_blacklist": req.Reason,
			"updated_by":             req.ActionBy,
			"updated_date":           util.Now(),
		}
	} else if req.IsBlacklist != nil {
		fieldsToUpdate = map[string]interface{}{
			"is_blacklist":           req.IsBlacklist,
			"reason_block_blacklist": req.Reason,
			"updated_by":             req.ActionBy,
			"updated_date":           util.Now(),
		}
	}

	err = s.BuyerRepo.UpdateStatus(req.Id, fieldsToUpdate)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	if req.IsActive != nil {
		fieldsToUpdate = map[string]interface{}{
			"is_active": req.IsActive,
		}
		err = s.UserRepo.UpdateStatus(util.Val(buyerDb.UserId), fieldsToUpdate)
		if err != nil {
			log.Error(err)
			return errs.NewError(http.StatusInternalServerError, err)
		}
	}

	return nil
}

func (s *memberService) GetBuyerRegistrationRequestFlowChart() (dto.BuyerRegistrationRequestFlowChartPageRespDto, error) {
	var newTotal int
	newBuyerRegistrationRequestFlowChartIndividualDB, err := s.BuyerRegistrationRequestRepo.GetNewBuyerRegistrationRequestFlowChartIndividual()
	if err != nil {
		log.Error(err)
		return dto.BuyerRegistrationRequestFlowChartPageRespDto{}, errs.NewError(http.StatusInternalServerError, err)
	}

	if newBuyerRegistrationRequestFlowChartIndividualDB != nil {
		if newBuyerRegistrationRequestFlowChartIndividualDB.Total != nil {
			newTotal = newTotal + *newBuyerRegistrationRequestFlowChartIndividualDB.Total
		}
	}

	newBuyerRegistrationRequestFlowChartForeignerDB, err := s.BuyerRegistrationRequestRepo.GetNewBuyerRegistrationRequestFlowChartForeigner()
	if err != nil {
		log.Error(err)
		return dto.BuyerRegistrationRequestFlowChartPageRespDto{}, errs.NewError(http.StatusInternalServerError, err)
	}

	if newBuyerRegistrationRequestFlowChartForeignerDB != nil {
		if newBuyerRegistrationRequestFlowChartForeignerDB.Total != nil {
			newTotal = newTotal + *newBuyerRegistrationRequestFlowChartForeignerDB.Total
		}
	}

	newBuyerRegistrationRequestFlowChartLegalEntityDB, err := s.BuyerRegistrationRequestRepo.GetNewBuyerRegistrationRequestFlowChartLegalEntity()
	if err != nil {
		log.Error(err)
		return dto.BuyerRegistrationRequestFlowChartPageRespDto{}, errs.NewError(http.StatusInternalServerError, err)
	}

	if newBuyerRegistrationRequestFlowChartLegalEntityDB != nil {
		if newBuyerRegistrationRequestFlowChartLegalEntityDB.Total != nil {
			newTotal = newTotal + *newBuyerRegistrationRequestFlowChartLegalEntityDB.Total
		}
	}

	var waitingTotal int
	waitingBuyerRegistrationRequestFlowChartWaitingDB, err := s.BuyerRegistrationRequestRepo.GetWaitingBuyerRegistrationRequestFlowChartWaiting()
	if err != nil {
		log.Error(err)
		return dto.BuyerRegistrationRequestFlowChartPageRespDto{}, errs.NewError(http.StatusInternalServerError, err)
	}

	if waitingBuyerRegistrationRequestFlowChartWaitingDB != nil {
		if waitingBuyerRegistrationRequestFlowChartWaitingDB.Total != nil {
			waitingTotal = waitingTotal + *waitingBuyerRegistrationRequestFlowChartWaitingDB.Total
		}
	}

	waitingBuyerRegistrationRequestFlowChartApproveDB, err := s.BuyerRegistrationRequestRepo.GetWaitingBuyerRegistrationRequestFlowChartApprove()
	if err != nil {
		log.Error(err)
		return dto.BuyerRegistrationRequestFlowChartPageRespDto{}, errs.NewError(http.StatusInternalServerError, err)
	}

	if waitingBuyerRegistrationRequestFlowChartApproveDB != nil {
		if waitingBuyerRegistrationRequestFlowChartApproveDB.Total != nil {
			waitingTotal = waitingTotal + *waitingBuyerRegistrationRequestFlowChartApproveDB.Total
		}
	}

	waitingBuyerRegistrationRequestFlowChartRejectDB, err := s.BuyerRegistrationRequestRepo.GetWaitingBuyerRegistrationRequestFlowChartReject()
	if err != nil {
		log.Error(err)
		return dto.BuyerRegistrationRequestFlowChartPageRespDto{}, errs.NewError(http.StatusInternalServerError, err)
	}

	if waitingBuyerRegistrationRequestFlowChartRejectDB != nil {
		if waitingBuyerRegistrationRequestFlowChartRejectDB.Total != nil {
			waitingTotal = waitingTotal + *waitingBuyerRegistrationRequestFlowChartRejectDB.Total
		}
	}

	var editingTotal int
	editingBuyerEditProfileRequestFlowChartWaitingDB, err := s.BuyerRegistrationRequestRepo.GetEditingBuyerEditProfileRequestFlowChartWaiting()
	if err != nil {
		log.Error(err)
		return dto.BuyerRegistrationRequestFlowChartPageRespDto{}, errs.NewError(http.StatusInternalServerError, err)
	}

	if editingBuyerEditProfileRequestFlowChartWaitingDB != nil {
		if editingBuyerEditProfileRequestFlowChartWaitingDB.Total != nil {
			editingTotal = editingTotal + *editingBuyerEditProfileRequestFlowChartWaitingDB.Total
		}
	}

	editingBuyerEditProfileRequestFlowChartApproveDB, err := s.BuyerRegistrationRequestRepo.GetEditingBuyerEditProfileRequestFlowChartApprove()
	if err != nil {
		log.Error(err)
		return dto.BuyerRegistrationRequestFlowChartPageRespDto{}, errs.NewError(http.StatusInternalServerError, err)
	}

	if editingBuyerEditProfileRequestFlowChartApproveDB != nil {
		if editingBuyerEditProfileRequestFlowChartApproveDB.Total != nil {
			editingTotal = editingTotal + *editingBuyerEditProfileRequestFlowChartApproveDB.Total
		}
	}

	editingBuyerEditProfileRequestFlowChartRejectDB, err := s.BuyerRegistrationRequestRepo.GetEditingBuyerEditProfileRequestFlowChartReject()
	if err != nil {
		log.Error(err)
		return dto.BuyerRegistrationRequestFlowChartPageRespDto{}, errs.NewError(http.StatusInternalServerError, err)
	}

	if editingBuyerEditProfileRequestFlowChartRejectDB != nil {
		if editingBuyerEditProfileRequestFlowChartRejectDB.Total != nil {
			editingTotal = editingTotal + *editingBuyerEditProfileRequestFlowChartRejectDB.Total
		}
	}

	var budgetIncreasingTotal int
	budgetIncreasingCreditLimitRequestFlowChartAutoDB, err := s.BuyerRegistrationRequestRepo.GetBudgetIncreasingCreditLimitRequestFlowChartAuto()
	if err != nil {
		log.Error(err)
		return dto.BuyerRegistrationRequestFlowChartPageRespDto{}, errs.NewError(http.StatusInternalServerError, err)
	}

	if budgetIncreasingCreditLimitRequestFlowChartAutoDB != nil {
		if budgetIncreasingCreditLimitRequestFlowChartAutoDB.Total != nil {
			budgetIncreasingTotal = budgetIncreasingTotal + *budgetIncreasingCreditLimitRequestFlowChartAutoDB.Total
		}
	}

	budgetIncreasingCreditLimitRequestFlowChartWaitingDB, err := s.BuyerRegistrationRequestRepo.GetBudgetIncreasingCreditLimitRequestFlowChartWaiting()
	if err != nil {
		log.Error(err)
		return dto.BuyerRegistrationRequestFlowChartPageRespDto{}, errs.NewError(http.StatusInternalServerError, err)
	}

	if budgetIncreasingCreditLimitRequestFlowChartWaitingDB != nil {
		if budgetIncreasingCreditLimitRequestFlowChartWaitingDB.Total != nil {
			budgetIncreasingTotal = budgetIncreasingTotal + *budgetIncreasingCreditLimitRequestFlowChartWaitingDB.Total
		}
	}

	budgetIncreasingCreditLimitRequestFlowChartApproveDB, err := s.BuyerRegistrationRequestRepo.GetBudgetIncreasingCreditLimitRequestFlowChartApprove()
	if err != nil {
		log.Error(err)
		return dto.BuyerRegistrationRequestFlowChartPageRespDto{}, errs.NewError(http.StatusInternalServerError, err)
	}

	if budgetIncreasingCreditLimitRequestFlowChartApproveDB != nil {
		if budgetIncreasingCreditLimitRequestFlowChartApproveDB.Total != nil {
			budgetIncreasingTotal = budgetIncreasingTotal + *budgetIncreasingCreditLimitRequestFlowChartApproveDB.Total
		}
	}

	budgetIncreasingCreditLimitRequestFlowChartRejectDB, err := s.BuyerRegistrationRequestRepo.GetBudgetIncreasingCreditLimitRequestFlowChartReject()
	if err != nil {
		log.Error(err)
		return dto.BuyerRegistrationRequestFlowChartPageRespDto{}, errs.NewError(http.StatusInternalServerError, err)
	}

	if budgetIncreasingCreditLimitRequestFlowChartRejectDB != nil {
		if budgetIncreasingCreditLimitRequestFlowChartRejectDB.Total != nil {
			budgetIncreasingTotal = budgetIncreasingTotal + *budgetIncreasingCreditLimitRequestFlowChartRejectDB.Total
		}
	}

	resp := dto.BuyerRegistrationRequestFlowChartPageRespDto{
		NewBuyerRegistrationRequest: &dto.FlowchartCustomerTypeCalAllRespDto{
			Total:       &newTotal,
			Individual:  getFlowChartCustomerTypeData(newBuyerRegistrationRequestFlowChartIndividualDB, newTotal),
			Foreigner:   getFlowChartCustomerTypeData(newBuyerRegistrationRequestFlowChartForeignerDB, newTotal),
			LegalEntity: getFlowChartCustomerTypeData(newBuyerRegistrationRequestFlowChartLegalEntityDB, newTotal),
		},
		WaitingBuyerRegistrationRequest: &dto.FlowchartApprovalStatusCalAllRespDto{
			Total:   &waitingTotal,
			Waiting: getFlowChartApprovalStatusData(waitingBuyerRegistrationRequestFlowChartWaitingDB, waitingTotal, constant.SUMMARY_CHART_WAITING_TYPE),
			Approve: getFlowChartApprovalStatusData(waitingBuyerRegistrationRequestFlowChartApproveDB, waitingTotal, constant.SUMMARY_CHART_WAITING_TYPE),
			Reject:  getFlowChartApprovalStatusData(waitingBuyerRegistrationRequestFlowChartRejectDB, waitingTotal, constant.SUMMARY_CHART_WAITING_TYPE),
		},
		EditingBuyerEditProfileRequest: &dto.FlowchartApprovalStatusCalAllRespDto{
			Total:   &editingTotal,
			Waiting: getFlowChartApprovalStatusData(editingBuyerEditProfileRequestFlowChartWaitingDB, editingTotal, constant.SUMMARY_CHART_EDITING_TYPE),
			Approve: getFlowChartApprovalStatusData(editingBuyerEditProfileRequestFlowChartApproveDB, editingTotal, constant.SUMMARY_CHART_EDITING_TYPE),
			Reject:  getFlowChartApprovalStatusData(editingBuyerEditProfileRequestFlowChartRejectDB, editingTotal, constant.SUMMARY_CHART_EDITING_TYPE),
		},
		BudgetIncreasingCreditLimitRequest: &dto.BudgetIncreasingCreditLimitRequestAllRespDto{
			Total:   &budgetIncreasingTotal,
			Auto:    getFlowChartApprovalStatusData(budgetIncreasingCreditLimitRequestFlowChartAutoDB, budgetIncreasingTotal, constant.SUMMARY_CHART_BUDGET_INCREASING_TYPE),
			Waiting: getFlowChartApprovalStatusData(budgetIncreasingCreditLimitRequestFlowChartWaitingDB, budgetIncreasingTotal, constant.SUMMARY_CHART_BUDGET_INCREASING_TYPE),
			Approve: getFlowChartApprovalStatusData(budgetIncreasingCreditLimitRequestFlowChartApproveDB, budgetIncreasingTotal, constant.SUMMARY_CHART_BUDGET_INCREASING_TYPE),
			Reject:  getFlowChartApprovalStatusData(budgetIncreasingCreditLimitRequestFlowChartRejectDB, budgetIncreasingTotal, constant.SUMMARY_CHART_BUDGET_INCREASING_TYPE),
		},
	}
	return resp, nil
}

func getFlowChartCustomerTypeData(db *dto.RequestFlowChartRepoDto, total int) *dto.FlowchartCustomerTypeCalRespDto {
	if db == nil {
		return &dto.FlowchartCustomerTypeCalRespDto{
			CustomerTypeTh:      nil,
			CustomerTypeEn:      nil,
			CustomerTypePercent: nil,
		}
	}

	var nameTh, nameEn string
	if db.CustomerTypeId != nil {
		switch *db.CustomerTypeCode {
		case constant.SUMMARY_CHART_INDIVIDUAL_CUSTOMER_TYPE_CODE:
			if db.CountryCode != nil {
				switch *db.CountryCode {
				case constant.MASTER_COUNTRY_THAILAND_CODE:
					nameTh = "บุคคลธรรมดา"
				default:
					nameTh = "ชาวต่างชาติ"
				}
			}
		case constant.SUMMARY_CHART_LEGAL_ENTITY_CUSTOMER_TYPE_CODE:
			nameTh = "นิติบุคคล"
		}
	}

	var percent float64
	if total != 0 && db.TotalPerType != nil {
		percent = (100.0 * float64(*db.TotalPerType)) / float64(total)
		percent = (math.Round(percent*100) / 100)
	}

	return &dto.FlowchartCustomerTypeCalRespDto{
		CustomerTypeTh:      &nameTh,
		CustomerTypeEn:      &nameEn,
		CustomerTypePercent: &percent,
	}
}

func getFlowChartApprovalStatusData(db *dto.RequestFlowChartRepoDto, total int, reqType string) *dto.FlowchartApprovalStatusCalRespDto {
	if db == nil {
		return &dto.FlowchartApprovalStatusCalRespDto{
			ApprovalStatusTh:      nil,
			ApprovalStatusEn:      nil,
			ApprovalStatusPercent: nil,
		}
	}

	var nameTh, nameEn string
	switch reqType {
	case constant.SUMMARY_CHART_WAITING_TYPE, constant.SUMMARY_CHART_EDITING_TYPE:
		if db.ApprovalStatus != nil {
			switch *db.ApprovalStatus {
			case constant.SUMMARY_CHART_WAITING_APPROVAL_STATUS:
				nameEn = "Waiting"
			case constant.SUMMARY_CHART_APPROVED_APPROVAL_STATUS:
				nameEn = "Approve"
			case constant.SUMMARY_CHART_REJECTED_APPROVAL_STATUS:
				nameEn = "Reject"
			}
		}
	case constant.SUMMARY_CHART_BUDGET_INCREASING_TYPE:
		switch *db.ApprovalStatus {
		case constant.SUMMARY_CHART_AUTO_APPROVAL_STATUS:
			nameTh = "คำขออัตโนมัติ A B"
		case constant.SUMMARY_CHART_WAITING_APPROVAL_STATUS:
			nameTh = "คำขอ Waiting C D"
		case constant.SUMMARY_CHART_APPROVED_APPROVAL_STATUS:
			nameTh = "คำขอ Approve"
		case constant.SUMMARY_CHART_REJECTED_APPROVAL_STATUS:
			nameTh = "คำขอ Reject"
		}
	}

	var percent float64
	if total != 0 && db.TotalPerStatus != nil {
		percent = (100.0 * float64(*db.TotalPerStatus)) / float64(total)
		percent = (math.Round(percent*100) / 100)
	}

	return &dto.FlowchartApprovalStatusCalRespDto{
		ApprovalStatusTh:      &nameTh,
		ApprovalStatusEn:      &nameEn,
		ApprovalStatusPercent: &percent,
	}
}
