package notificationconfig

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/notification_config"
	service "content-service/internal/service/notification_config"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewNotificationConfigRepository(db)
	service := service.NewNotificationConfigService(repo)
	handler := &Handler{Service: service}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("notification-configs")
	route.Get("", h.GetNotificationConfig)
	route.Put("", h.UpdateNotificationConfig)
}
