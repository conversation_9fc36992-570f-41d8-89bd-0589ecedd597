package userbiddernumbers

import (
	"backend-common-lib/model"
	"content-service/internal/global"
	"net/http"

	service "content-service/internal/service/user_bidder_numbers"

	"github.com/gofiber/fiber/v2"
)

type Handler struct {
	Service   service.UserBidderNumbersService
	ErpConfig global.ErpConfig
}

func (h *Handler) GetUserBidderNumberByBidderNumber(c *fiber.Ctx) error {
	bidderNumberIdStr := c.Params("bidderNumber")
	lotSouLotLineId := c.QueryInt("lotSouLotLineId")

	res, err := h.Service.GetUserBidderNumberByBidderNumber(bidderNumberIdStr, lotSouLotLineId)
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}
