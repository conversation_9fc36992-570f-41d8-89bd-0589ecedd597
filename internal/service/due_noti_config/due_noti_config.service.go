package service

import (
	"backend-common-lib/model"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/due_noti_config"
	repositoryExcludedBuyer "content-service/internal/repository/due_noti_config_excluded_buyer"
)

type dueNotiConfigService struct {
	Repo              repository.DueNotiConfigRepository
	ExcludedBuyerRepo repositoryExcludedBuyer.DueNotiConfigExcludedBuyerRepository
}

type DueNotiConfigService interface {
	GetDueNotiConfig(req model.PagingRequest, notiType string) (model.PagingModel[dto.DueNotiConfigDto], error)
	GetDueNotiConfigByID(id int) (dto.DueNotiConfigDto, error)
	CreateDueNotiConfig(req dto.DueNotiConfigDto, notiType string, actionBy *int) error
	UpdateDueNotiConfig(req dto.DueNotiConfigDto, actionBy *int) error
	UpdateDueNotiConfigStatus(req dto.DueNotiConfigDto, actionBy *int) error
	DeleteDueNotiConfig(id int) error
}

func NewDueNotiConfigService(
	repo repository.DueNotiConfigRepository,
	repoExcludedBuyerRepo repositoryExcludedBuyer.DueNotiConfigExcludedBuyerRepository,
) DueNotiConfigService {
	return &dueNotiConfigService{
		Repo:              repo,
		ExcludedBuyerRepo: repoExcludedBuyerRepo,
	}
}
