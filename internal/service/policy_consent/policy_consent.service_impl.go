package service

import (
	constant "backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	contentConst "content-service/constant"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"fmt"
	"net/http"

	"gorm.io/gorm"
)

func (s *policyConsentService) GetPolicyConsent(req model.PagingRequest, reqConsentType string) (dto.PolicyPageResDto, error) {
	resp := dto.PolicyPageResDto{}
	consentType, err := s.getConsentType(reqConsentType)
	if err != nil {
		return resp, err
	}

	maxMajorVersion := util.Ptr(0)
	//NOTE - get max major version
	latest, err := s.Repo.FindMaxVersionByFilters(map[string]interface{}{
		"consent_type": consentType,
	})
	if err == nil {
		maxMajorVersion = latest.VersionMajor
	} else if !errs.IsGormNotFound(err) {
		return resp, err
	}

	resp.MaxVersionMajor = maxMajorVersion

	//NOTE - Get List
	result, err := s.Repo.FindAllPolicyConsent(req, consentType)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountAllPolicyConsent(consentType)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.PolicyConsentDto, len(result))
	for i, v := range result {
		temp := util.MapToWithCreatedByAndUpdatedBy[dto.PolicyConsentDto](v)
		temp.Version = util.Ptr(fmt.Sprintf("%d"+"."+"%d", util.Val(temp.VersionMajor), util.Val(temp.VersionMinor)))
		mapResult[i] = temp
	}

	//NOTE - Response
	resp.PagingModel = *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit)

	return resp, nil
}

func (s *policyConsentService) GetPolicyConsentByID(id int) (dto.PolicyConsentDto, error) {
	resp := dto.PolicyConsentDto{}
	result, err := s.Repo.FindPolicyConsentById(id)
	if err != nil {
		if errs.IsGormNotFound(err) {
			return dto.PolicyConsentDto{}, errs.NewError(http.StatusNotFound, err)
		}
		return dto.PolicyConsentDto{}, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	resp = util.MapToWithCreatedByAndUpdatedBy[dto.PolicyConsentDto](result)
	resp.Version = util.Ptr(fmt.Sprintf("%d"+"."+"%d", util.Val(resp.VersionMajor), util.Val(resp.VersionMinor)))
	return resp, nil
}

func validatePolicyConsent(policyConsent dto.PolicyConsentReqDto) error {
	if policyConsent.ConsentName == nil || util.Val(policyConsent.ConsentName) == "" {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("consent name is required"))
	}
	if policyConsent.Description == nil || util.Val(policyConsent.Description) == "" {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("description is required"))
	}
	if policyConsent.VersionMajor == nil || util.Val(policyConsent.VersionMajor) == 0 {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("version have to more than 0"))
	}
	if policyConsent.VersionMinor == nil {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("version minor is required"))
	}
	if policyConsent.StartDate == nil {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("start date is required"))
	}
	if policyConsent.PolicyTextTh == nil || util.Val(policyConsent.PolicyTextTh) == "" {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("policy text th is required"))
	}
	if policyConsent.PolicyTextEn == nil || util.Val(policyConsent.PolicyTextEn) == "" {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("policy text en is required"))
	}
	return nil
}

func (s *policyConsentService) CreatePolicyConsent(req dto.PolicyConsentReqDto, reqConsentType string) error {
	now := util.Now()

	consentType, err := s.getConsentType(reqConsentType)
	if err != nil {
		return err
	}

	//NOTE - Check Required fields
	err = validatePolicyConsent(req)
	if err != nil {
		return err
	}

	//NOTE - Check Start Date in Range input
	exist, err := s.Repo.FindNextClosestStartDateInRange(req.StartDate, req.EndDate, consentType)
	if err == nil && exist != nil {
		return errs.NewBusinessError(http.StatusBadRequest, constant.Invalidate, fmt.Sprintf("%d.%d", util.Val(exist.VersionMajor), util.Val(exist.VersionMinor)), "error.policyConsent.startDateInRange")
	} else if err != nil && !errs.IsGormNotFound(err) {
		return errs.NewError(http.StatusInternalServerError, err)
	}

	maxMajorVersion := util.Ptr(0)
	//NOTE - Check Version increment
	latest, err := s.Repo.FindMaxVersionByFilters(map[string]interface{}{
		"consent_type": consentType,
	})
	if err == nil {
		maxMajorVersion = latest.VersionMajor
	} else if !errs.IsGormNotFound(err) {
		return err
	}
	if util.Val(req.VersionMajor) != util.Val(maxMajorVersion)+1 || util.Val(req.VersionMinor) != 0 {
		return errs.NewBusinessError(http.StatusBadRequest, constant.Invalidate, "version is not correct", "")
	}

	//NOTE - find record that overlap
	overlapRecord, err := s.Repo.FindEndDateInRange(req.StartDate, req.EndDate, consentType)
	if err != nil {
		return err
	}

	//NOTE - Create Payment Due Notification
	entityPolicyConsent := util.MapToPtr[entity.PolicyConsent](req)
	entityPolicyConsent.ConsentType = consentType
	if entityPolicyConsent == nil {
		return errs.NewBusinessError(http.StatusBadRequest, constant.BadRequest, "Invalid request data", "")
	}

	entityPolicyConsent.BaseEntity = &model.BaseEntity{
		CreatedBy:   req.ActionBy,
		CreatedDate: now,
		UpdatedBy:   req.ActionBy,
		UpdatedDate: &now,
	}

	errTx := util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {

		//NOTE - update end date of overlap record (should be only 1 record)
		for _, record := range overlapRecord {
			fieldsToUpdate := map[string]interface{}{
				"end_date":     req.StartDate.AddDate(0, 0, -1),
				"updated_by":   req.ActionBy,
				"updated_date": &now,
			}

			affectedRows, updatedRecord, err := s.Repo.UpdatesPolicyConsentFieldsWhere(tx, fieldsToUpdate, "id = ?", record.Id)
			if err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
			if affectedRows == 0 {
				return errs.NewBusinessError(http.StatusNotFound, constant.NotFound, fmt.Sprintf("id of overlap record %d not found", record.Id), "")
			}

			//NOTE - Create Policy Consent Log
			entityPolicyConsentLog := &entity.PolicyConsentLog{
				PolicyConsent:   updatedRecord,
				PolicyConsentId: updatedRecord.Id,
			}
			entityPolicyConsentLog.Id = 0 // Reset ID to ensure a new record is created
			if err := s.Repo.InsertPolicyConsentLog(tx, entityPolicyConsentLog); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}

		if err := s.Repo.InsertPolicyConsent(tx, entityPolicyConsent); err != nil {
			return errs.NewError(http.StatusInternalServerError, err)
		}

		//NOTE - Create Policy Consent Log
		entityPolicyConsentLog := &entity.PolicyConsentLog{
			PolicyConsent:   entityPolicyConsent,
			PolicyConsentId: entityPolicyConsent.Id,
		}
		entityPolicyConsentLog.Id = 0 // Reset ID to ensure a new record is created
		if err := s.Repo.InsertPolicyConsentLog(tx, entityPolicyConsentLog); err != nil {
			return errs.NewError(http.StatusInternalServerError, err)
		}

		return nil
	})

	if errTx != nil {
		return errTx
	}
	return nil
}

func (s *policyConsentService) UpdatePolicyConsent(req dto.PolicyConsentReqDto, reqConsentType string) error {
	consentType, err := s.getConsentType(reqConsentType)
	if err != nil {
		return err
	}

	//NOTE - Check Required fields
	if err := validatePolicyConsent(req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	//NOTE - Check Start Date in Range input
	exist, err := s.Repo.FindNextClosestStartDateInRange(req.StartDate, req.EndDate, consentType, req.Id)
	if err == nil && exist != nil {
		return errs.NewBusinessError(http.StatusBadRequest, constant.Invalidate, fmt.Sprintf("%d.%d", util.Val(exist.VersionMajor), util.Val(exist.VersionMinor)), "error.policyConsent.startDateInRange")
	} else if err != nil && !errs.IsGormNotFound(err) {
		return errs.NewError(http.StatusInternalServerError, err)
	}

	maxMajorVersion, maxMinorVersion := util.Ptr(0), util.Ptr(0)
	//NOTE - Check Version increment
	latest, err := s.Repo.FindMaxVersionByFilters(map[string]interface{}{
		"consent_type":  consentType,
		"version_major": req.VersionMajor,
	})
	if err == nil {
		maxMajorVersion = latest.VersionMajor
		maxMinorVersion = latest.VersionMinor
	} else if !errs.IsGormNotFound(err) {
		return err
	}
	if util.Val(req.VersionMajor) != util.Val(maxMajorVersion) || util.Val(req.VersionMinor) != util.Val(maxMinorVersion)+1 {
		return errs.NewBusinessError(http.StatusBadRequest, constant.Invalidate, "version is not correct", "")
	}

	//NOTE - find record that overlap
	overlapRecord, err := s.Repo.FindEndDateInRange(req.StartDate, req.EndDate, consentType, req.Id)
	if err != nil {
		return err
	}

	now := util.Now()
	fieldsToUpdate := map[string]interface{}{
		"consent_name":   req.ConsentName,
		"description":    req.Description,
		"version_major":  req.VersionMajor,
		"version_minor":  req.VersionMinor,
		"start_date":     req.StartDate,
		"end_date":       req.EndDate,
		"is_active":      req.IsActive,
		"policy_text_th": req.PolicyTextTh,
		"policy_text_en": req.PolicyTextEn,
		"updated_by":     req.ActionBy,
		"updated_date":   &now,
	}

	errTx := util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		//NOTE - update end date of overlap record (should be only 1 record)
		for _, record := range overlapRecord {
			fieldsToUpdate := map[string]interface{}{
				"end_date":     req.StartDate.AddDate(0, 0, -1),
				"updated_by":   req.ActionBy,
				"updated_date": &now,
			}

			affectedRows, updatedRecord, err := s.Repo.UpdatesPolicyConsentFieldsWhere(tx, fieldsToUpdate, "id = ?", record.Id)
			if err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
			if affectedRows == 0 {
				return errs.NewBusinessError(http.StatusNotFound, constant.NotFound, fmt.Sprintf("id of overlap record %d not found", record.Id), "")
			}

			//NOTE - Create Policy Consent Log
			entityPolicyConsentLog := &entity.PolicyConsentLog{
				PolicyConsent:   updatedRecord,
				PolicyConsentId: updatedRecord.Id,
			}
			entityPolicyConsentLog.Id = 0 // Reset ID to ensure a new record is created
			if err := s.Repo.InsertPolicyConsentLog(tx, entityPolicyConsentLog); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}

		affectedRows, updatedRecord, err := s.Repo.UpdatesPolicyConsentFieldsWhere(tx, fieldsToUpdate, "id = ?", req.Id)
		if err != nil {
			return errs.NewError(http.StatusInternalServerError, err)
		}
		if affectedRows == 0 {
			return errs.NewBusinessError(http.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
		}

		//NOTE - Create Policy Consent Log
		entityPolicyConsentLog := &entity.PolicyConsentLog{
			PolicyConsent:   updatedRecord,
			PolicyConsentId: updatedRecord.Id,
		}
		entityPolicyConsentLog.Id = 0 // Reset ID to ensure a new record is created
		if err := s.Repo.InsertPolicyConsentLog(tx, entityPolicyConsentLog); err != nil {
			return errs.NewError(http.StatusInternalServerError, err)
		}

		return nil
	})

	if errTx != nil {
		return errTx
	}
	return nil
}

func (s *policyConsentService) UpdatePolicyConsentStatus(req dto.PolicyConsentReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	errTx := util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		affectedRows, updatedRecord, err := s.Repo.UpdatesPolicyConsentFieldsWhere(tx, fieldsToUpdate, "id = ?", req.Id)
		if err != nil {
			return errs.NewError(http.StatusInternalServerError, err)
		}
		if affectedRows == 0 {
			return errs.NewBusinessError(http.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
		}

		//NOTE - Create Policy Consent Log
		entityPolicyConsentLog := &entity.PolicyConsentLog{
			PolicyConsent:   updatedRecord,
			PolicyConsentId: updatedRecord.Id,
		}
		entityPolicyConsentLog.Id = 0 // Reset ID to ensure a new record is created
		if err := s.Repo.InsertPolicyConsentLog(tx, entityPolicyConsentLog); err != nil {
			return errs.NewError(http.StatusInternalServerError, err)
		}

		return nil
	})

	if errTx != nil {
		return errTx
	}
	return nil
}

func (s *policyConsentService) DeletePolicyConsent(id int) error {

	errTx := util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		affectedRows, err := s.Repo.DeletePolicyConsentByID(tx, id)
		if err != nil {
			return errs.NewError(http.StatusInternalServerError, err)
		}
		if affectedRows == 0 {
			return errs.NewBusinessError(http.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", id), "")
		}

		//NOTE - Get Deleted Record
		deletedRecord, err := s.Repo.FindUnscopedPolicyConsentById(id)

		//NOTE - Create Policy Consent Log
		entityPolicyConsentLog := &entity.PolicyConsentLog{
			PolicyConsent:   util.Ptr(deletedRecord),
			PolicyConsentId: deletedRecord.Id,
		}
		entityPolicyConsentLog.Id = 0 // Reset ID to ensure a new record is created
		if err := s.Repo.InsertPolicyConsentLog(tx, entityPolicyConsentLog); err != nil {
			return errs.NewError(http.StatusInternalServerError, err)
		}
		return nil
	})

	if errTx != nil {
		return errTx
	}
	return nil
}

func (s *policyConsentService) ValidateOverlapStartDate(req dto.PolicyConsentReqDto, reqConsentType string) error {
	consentType, err := s.getConsentType(reqConsentType)
	if err != nil {
		return err
	}

	//NOTE - Check Start Date in Range input
	exist, err := s.Repo.FindNextClosestStartDateInRange(req.StartDate, req.EndDate, consentType, req.Id)
	if err == nil && exist != nil {
		return errs.NewBusinessError(http.StatusBadRequest, constant.Invalidate, fmt.Sprintf("%d.%d", util.Val(exist.VersionMajor), util.Val(exist.VersionMinor)), "error.policyConsent.startDateInRange")
	} else if err != nil && !errs.IsGormNotFound(err) {
		return errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - find record that overlap
	overlapRecord, err := s.Repo.FindEndDateInRange(req.StartDate, req.EndDate, consentType, req.Id)
	if err != nil {
		return err
	}
	if len(overlapRecord) > 0 {
		return errs.NewBusinessError(http.StatusConflict, constant.Invalidate, util.Val(overlapRecord[0].ConsentName), "warning.policyConsent.endDateInRange")
	}
	return nil
}

func (s *policyConsentService) getConsentType(reqConsentType string) (*string, error) {
	var consentType *string
	switch reqConsentType {
	case contentConst.PATH_PDPA:
		consentType = util.Ptr(contentConst.CONSENT_TYPE_BUYER_PDPA)
	case contentConst.PATH_MARKETING:
		consentType = util.Ptr(contentConst.CONSENT_TYPE_BUYER_MARKETING)
	case contentConst.PATH_PROXY_BID:
		consentType = util.Ptr(contentConst.CONSENT_TYPE_BUYER_PROXY_BID)
	case contentConst.PATH_WITHHOLDING_TAX:
		consentType = util.Ptr(contentConst.CONSENT_TYPE_BUYER_WITHHOLDING_TAX)
	case contentConst.PATH_SELLER_PDPA:
		consentType = util.Ptr(contentConst.CONSENT_TYPE_SELLER_PDPA)
	default:
		return nil, errs.NewError(http.StatusBadRequest, fmt.Errorf("invalid consent type"))
	}
	return consentType, nil
}
