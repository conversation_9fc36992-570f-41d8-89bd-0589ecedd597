package repository

import (
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

func (r *buyerAddressRepositoryImpl) GetByIdAndAddressType(id int, addressType int) (entity.BuyerAddress, error) {
	var buyerAddress entity.BuyerAddress
	err := r.DB.Table("buyer_address").Select(
		`buyer_address.*,
		sd.description_th AS sub_district_description_th,
		sd.description_en AS sub_district_description_en,
		d.description_th AS district_description_th,
		d.description_en AS district_description_en,
		p.description_th AS province_description_th,
		p.description_en AS province_description_en,
		c.description_th AS country_description_th,
		c.description_en AS country_description_en`).
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_sub_district) AS sd ON sd.id = buyer_address.sub_district_id").
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_district) AS d ON d.id = buyer_address.district_id").
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_city) AS p ON p.id = buyer_address.province_id").
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_country) AS c ON c.id = buyer_address.country_id").
		Where("buyer_address.buyer_id = ? AND buyer_address.address_type = ?", id, addressType).First(&buyerAddress).Error
	return buyerAddress, err
}

func (r *buyerAddressRepositoryImpl) GetAllByIdsAndAddressType(ids []int, addressType int) ([]*entity.BuyerAddress, error) {
	var buyerAddress []*entity.BuyerAddress
	err := r.DB.Table("buyer_address").Select(
		`buyer_address.*,
		sd.description_th AS sub_district_description_th,
		sd.description_en AS sub_district_description_en,
		d.description_th AS district_description_th,
		d.description_en AS district_description_en,
		p.description_th AS province_description_th,
		p.description_en AS province_description_en,
		c.description_th AS country_description_th,
		c.description_en AS country_description_en`).
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_sub_district) AS sd ON sd.id = buyer_address.sub_district_id").
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_district) AS d ON d.id = buyer_address.district_id").
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_city) AS p ON p.id = buyer_address.province_id").
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_country) AS c ON c.id = buyer_address.country_id").
		Where("buyer_address.buyer_id IN ? AND buyer_address.address_type = ?", ids, addressType).Find(&buyerAddress).Error
	return buyerAddress, err
}

func (r *buyerAddressRepositoryImpl) UpdateAllFields(buyerAddress entity.BuyerAddress) error {
	if err := r.DB.Save(buyerAddress).Error; err != nil {
		return err
	}
	return nil
}

func (r *buyerAddressRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
