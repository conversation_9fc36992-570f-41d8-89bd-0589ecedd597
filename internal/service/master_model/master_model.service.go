package service

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_model"
)

type masterModelService struct {
	Repo repository.MasterModelRepository
}

type MasterModelService interface {
	SyncMasterModelFromErp(ActionBy *int, erpConfig global.ErpConfig) error
}

func NewMasterModelService(repo repository.MasterModelRepository) MasterModelService {
	return &masterModelService{Repo: repo}
}
