package masterbranch

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_asset_location"
	service "content-service/internal/service/master_asset_location"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewMasterAssetLocationRepository(db)
	service := service.NewMasterAssetLocationService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("asset-locations")
	route.Post("/", h.SearchMasterAssetLocationFilter)
	route.Put("/status/:id", h.UpdateMasterAssetLocationStatus)
	route.Post("/sync", h.SyncMasterAssetLocationFromErp)

}
