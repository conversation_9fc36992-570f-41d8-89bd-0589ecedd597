package registrationbuyerformconfig

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/registration_buyer_form_config"
	service "content-service/internal/service/registration_buyer_form_config"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewRegistrationBuyerFormConfigRepository(db)
	service := service.NewRegistrationBuyerFormConfigService(repo)
	handler := &Handler{Service: service}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("registration-buyer-form-config")
	route.Get("/", h.GetRegistrationBuyerFormConfig)
	route.Put("/:id", h.UpdateRegistrationBuyerFormConfig)
}
