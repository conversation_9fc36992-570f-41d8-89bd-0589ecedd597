package service

import (
	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"errors"
	"fmt"
	"net/http"

	"gorm.io/gorm"
)

func (s *buyerService) FindBuyerDetailsWithFilter(req dto.BuyerSearchReqDto) (dto.BuyerSearchRespDto, error) {
	responseDto := dto.BuyerSearchRespDto{}

	result, err := s.BuyerRepo.FindBuyerDetailsWithFilter(req)
	if result == nil {
		return responseDto, errs.NewBusinessError(http.StatusNotFound, constant.NotFound, fmt.Sprintf("buyer not found"), "")
	} else if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return responseDto, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	responseDto.UserId = result.UserId
	responseDto.BidderId = result.BidderId
	responseDto.CustomerNo = result.CustomerNo
	responseDto.IdentificationNumber = result.IdentificationNumber
	responseDto.PrefixNameId = util.Ptr(result.PrefixNameId)
	responseDto.PrefixNameTh = result.PrefixNameTh
	responseDto.PrefixNameEn = result.PrefixNameEn
	responseDto.FirstName = result.FirstName
	responseDto.MiddleName = result.MiddleName
	responseDto.LastName = result.LastName
	if result.CustomerGroupForJoin != nil {
		responseDto.CustomerGroup = result.CustomerGroupForJoin.DescriptionTh
		responseDto.CustomerGroupId = util.Ptr(result.CustomerGroupForJoin.ID)
	}

	return responseDto, nil
}
