FROM golang:1.24.2 AS builder
WORKDIR /app
COPY go.mod go.sum ./
COPY backend-common-lib/ ./backend-common-lib/
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o main ./cmd

FROM alpine:latest

RUN apk add --no-cache ca-certificates tzdata
ENV TZ=Asia/Bangkok
WORKDIR /app

COPY --from=builder /app/main .

# Copy configuration files
COPY ./conf ./conf

# Create cert directory for certificate files
RUN mkdir -p /app/cert

# Copy and setup entrypoint script
COPY ./entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh

ARG ENV_FILE
COPY ${ENV_FILE} .env

EXPOSE 8184
ENTRYPOINT ["/app/entrypoint.sh"]