package service

import (
	"content-service/internal/global"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/master_customer_seller_offer"
)

type masterCustomerSellerOfferService struct {
	Repo repository.MasterCustomerSellerOfferRepository
}

type MasterCustomerSellerOfferService interface {
	SearchMasterCustomerSellerOfferFilter(req dto.MasterCustomerSellerOfferPageReqDto) (dto.MasterCustomerSellerOfferPageRespDto[dto.MasterCustomerSellerOfferDto], error)
	UpdateMasterCustomerSellerOfferStatus(req dto.MasterCustomerSellerOfferUpdateReqDto) error
	SyncMasterCustomerSellerOfferFromErp(actionBy *int, erpConfig global.ErpConfig) error
}

func NewMasterCustomerSellerOfferService(repo repository.MasterCustomerSellerOfferRepository) MasterCustomerSellerOfferService {
	return &masterCustomerSellerOfferService{Repo: repo}
}
