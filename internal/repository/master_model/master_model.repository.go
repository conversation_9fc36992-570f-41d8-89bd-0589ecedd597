package repository

import (
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type masterModelRepositoryImpl struct {
	DB *gorm.DB
}

type MasterModelRepository interface {
	FindMasterModelAll() ([]entity.MasterModel, error)
	UpdateMasterModelAllFields(e *entity.MasterModel) error
	InsertMasterModelList(data []entity.MasterModel) error
	GetDB() *gorm.DB
}

func NewMasterModelRepository(db *gorm.DB) MasterModelRepository {
	return &masterModelRepositoryImpl{DB: db}
}
