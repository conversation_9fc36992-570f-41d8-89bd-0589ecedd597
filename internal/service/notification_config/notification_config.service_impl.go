package service

import (
	constant "backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"fmt"
	"net/http"

	"gorm.io/gorm"
)

func (s *notificationConfigService) GetNotificationConfig(userType string) ([]*dto.NotificationConfigDto, error) {
	resp := []*dto.NotificationConfigDto{}

	//NOTE - Get List
	result, err := s.Repo.FindAllNotificationConfigByUserType(util.Ptr(userType))
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	res := make([]*dto.NotificationConfigDto, 0, len(result))
	for _, v := range result {
		res = append(res, util.MapToPtr[dto.NotificationConfigDto](v))
	}

	return res, nil
}

func validateNotificationConfig(notificationConfig []*dto.NotificationConfigDto) error {
	for _, noti := range notificationConfig {
		if noti.IsSystemNoti && (util.Val(noti.SystemTemplateTh) == "" || util.Val(noti.SystemTemplateEn) == "") {
			return errs.NewBusinessError(http.StatusBadRequest, constant.BadRequest, "both language system notification template is required", "")
		}
		if noti.IsSmsNoti && (util.Val(noti.SmsTemplateTh) == "" || util.Val(noti.SmsTemplateEn) == "") {
			return errs.NewBusinessError(http.StatusBadRequest, constant.BadRequest, "both language sms notification template is required", "")
		}
		if noti.IsEmailNoti && (util.Val(noti.EmailTemplateTh) == "" || util.Val(noti.EmailTemplateEn) == "") {
			return errs.NewBusinessError(http.StatusBadRequest, constant.BadRequest, "both language email notification template is required", "")
		}
	}

	return nil
}

func (s *notificationConfigService) UpdateNotificationConfig(req dto.NotificationConfigReqDto, userType string) error {

	//NOTE - Check Required fields
	if err := validateNotificationConfig(req.NotiList); err != nil {
		return err
	}

	now := util.Now()
	errTx := util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		//NOTE - update end date of overlap record (should be only 1 record)
		for _, noti := range req.NotiList {
			fieldsToUpdate := map[string]interface{}{
				"system_template_th": noti.SystemTemplateTh,
				"sms_template_th":    noti.SmsTemplateTh,
				"email_template_th":  noti.EmailTemplateTh,
				"system_template_en": noti.SystemTemplateEn,
				"sms_template_en":    noti.SmsTemplateEn,
				"email_template_en":  noti.EmailTemplateEn,
				"is_system_noti":     noti.IsSystemNoti,
				"is_sms_noti":        noti.IsSmsNoti,
				"is_email_noti":      noti.IsEmailNoti,
				"updated_by":         req.ActionBy,
				"updated_date":       now,
			}

			affectedRows, _, err := s.Repo.UpdatesNotificationConfigFieldsWhere(tx, fieldsToUpdate, "id = ? and user_type = ?", noti.Id, userType)
			if err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
			if affectedRows == 0 {
				return errs.NewBusinessError(http.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", noti.Id), "")
			}
		}
		return nil
	})

	if errTx != nil {
		return errTx
	}
	return nil
}
