package masterpostcode

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_postcode"
	service "content-service/internal/service/master_postcode"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewMasterPostcodeRepository(db)
	service := service.NewMasterPostcodeService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}
func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("postcodes")

	route.Post("", h.SearchMasterPostcodeFilter)
	route.Put("/status/:id", h.UpdateMasterPostcodeStatus)
	route.Post("/sync", h.SyncMasterPostcodeFromErp)
	route.Get("/with-subdistrict", h.GetPostCodeBySubDistrictID)
}
