package repository

import (
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type buyerAddressRepositoryImpl struct {
	DB *gorm.DB
}

type BuyerAddressRepository interface {
	GetByIdAndAddressType(id int, addressType int) (entity.BuyerAddress, error)
	GetAllByIdsAndAddressType(ids []int, addressType int) ([]*entity.BuyerAddress, error)
	UpdateAllFields(buyerAddress entity.BuyerAddress) error

	GetDB() *gorm.DB
}

func NewBuyeAddressRepository(db *gorm.DB) BuyerAddressRepository {
	return &buyerAddressRepositoryImpl{DB: db}
}
