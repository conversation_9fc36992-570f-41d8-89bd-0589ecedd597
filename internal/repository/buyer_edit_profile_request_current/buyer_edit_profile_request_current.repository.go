package repository

import (
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type buyerEditProfileRequestCurrentRepositoryImpl struct {
	DB *gorm.DB
}

type BuyerEditProfileRequestCurrentRepository interface {
	GetByBuyerEditProfileRequestId(buyerEditProfileRequestId int) (entity.BuyerEditProfileRequestCurrent, error)
	Insert(entity entity.BuyerEditProfileRequestCurrent) error

	GetDB() *gorm.DB
}

func NewBuyeEditProfileRequestCurrentRepository(db *gorm.DB) BuyerEditProfileRequestCurrentRepository {
	return &buyerEditProfileRequestCurrentRepositoryImpl{DB: db}
}
