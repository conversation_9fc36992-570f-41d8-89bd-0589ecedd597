package repository

import (
	"content-service/internal/model/entity"
	"fmt"

	"gorm.io/gorm"
)

func (r *dueNotiConfigExcludedBuyerRepositoryImpl) GetTopExcludedBuyersByNotificationIds(ids []int, topN int) (map[int][]entity.DueNotiConfigExcludedBuyer, error) {
	result := []entity.DueNotiConfigExcludedBuyer{}

	subQuery := fmt.Sprintf(`
		SELECT * FROM (
			SELECT *,
			RANK() OVER (PARTITION BY due_noti_config_id ORDER BY id ASC) AS rank
			FROM due_noti_config_excluded_buyer
		) AS ranked
		WHERE ranked.rank <= %d
	`, topN)

	if err := r.DB.Table("(?) as top_buyers", gorm.Expr(subQuery)).
		Preload("Buyer").
		Where("due_noti_config_id IN ?", ids).Find(&result).Error; err != nil {
		return nil, err
	}

	buyersMap := make(map[int][]entity.DueNotiConfigExcludedBuyer)
	for _, b := range result {
		buyersMap[b.DueNotiConfigId] = append(buyersMap[b.DueNotiConfigId], b)
	}

	return buyersMap, nil
}

func (r *dueNotiConfigExcludedBuyerRepositoryImpl) GetExcludedBuyersByNotificationId(id int) ([]entity.DueNotiConfigExcludedBuyer, error) {
	var excludedBuyers []entity.DueNotiConfigExcludedBuyer

	err := r.DB.
		Preload("Buyer").
		Where("due_noti_config_id = ?", id).
		Find(&excludedBuyers).Error
	if err != nil {
		return nil, err
	}

	return excludedBuyers, nil
}

func (r *dueNotiConfigExcludedBuyerRepositoryImpl) BulkInsertExcludedBuyers(excludedBuyers []entity.DueNotiConfigExcludedBuyer) error {
	if len(excludedBuyers) == 0 {
		return nil
	}

	if err := r.DB.Create(&excludedBuyers).Error; err != nil {
		return err
	}

	return nil
}

func (r *dueNotiConfigExcludedBuyerRepositoryImpl) PermanentDeleteExcludedBuyers(dueNotiConfigId int) error {
	if err := r.DB.Where("due_noti_config_id = ?", dueNotiConfigId).
		Unscoped().Delete(&entity.DueNotiConfigExcludedBuyer{}).Error; err != nil {
		return err
	}
	return nil
}

func (r *dueNotiConfigExcludedBuyerRepositoryImpl) DeleteExcludedBuyerNotificationId(dueNotiConfigId int) error {
	if err := r.DB.Where("due_noti_config_id = ?", dueNotiConfigId).
		Delete(&entity.DueNotiConfigExcludedBuyer{}).Error; err != nil {
		return err
	}
	return nil
}

func (r *dueNotiConfigExcludedBuyerRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
