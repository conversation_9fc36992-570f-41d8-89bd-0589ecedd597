package service

import (
	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"fmt"
	"net/http"
)

func (s *passwordExpiryService) GetPasswordExpiry() ([]dto.PasswordExpiryDto, error) {
	resp := []dto.PasswordExpiryDto{}

	//NOTE - Get List
	result, err := s.Repo.FindAllPasswordExpiry()
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.PasswordExpiryDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.PasswordExpiryDto](v)
	}

	return mapResult, nil
}

func (s *passwordExpiryService) UpdatePasswordExpiry(req dto.PasswordExpiryReqDto) error {

	now := util.Now()
	fieldsToUpdate := map[string]interface{}{
		"password_expiry_days":      req.PasswordExpiryDays,
		"pin_expiry_days":           req.PinExpiryDays,
		"notify_before_expiry_days": req.NotifyBeforeExpiryDays,
		"updated_by":                req.ActionBy,
		"updated_date":              &now,
	}

	affectedRows, _, err := s.Repo.UpdatesPasswordExpiryFieldsWhere(fieldsToUpdate, req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(http.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}
	return nil
}
