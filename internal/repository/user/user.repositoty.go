package repository

import (
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type userRepositoryImpl struct {
	DB *gorm.DB
}

type UserRepository interface {
	GetById(id int) (entity.Users, error)
	UpdateStatus(userId int, fields map[string]interface{}) error
	UpdateStatusTx(tx *gorm.DB, userId int, fields map[string]interface{}) error

	GetDB() *gorm.DB
}

func NewUserRepository(db *gorm.DB) UserRepository {
	return &userRepositoryImpl{DB: db}
}
