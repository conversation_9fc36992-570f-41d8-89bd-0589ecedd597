package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type buyerRepositoryImpl struct {
	DB *gorm.DB
}

type BuyerRepository interface {
	GetById(id int) (entity.Buyer, error)
	GetByIdWithPrefix(id int) (entity.Buyer, error)
	GetAllByIds(ids []int) ([]*entity.Buyer, error)
	FindBuyerWithFilter(req dto.MemberSearchReqDto) ([]entity.Buyer, error)
	CountBuyerWithFilter(req dto.MemberSearchReqDto) (int64, error)
	UpdateAllFields(buyer entity.Buyer) error
	UpdateStatus(buyerId int, fields map[string]interface{}) error
	UpdateStatusTx(tx *gorm.DB, buyerId int, fields map[string]interface{}) error

	FindBuyerDetailsWithFilter(req dto.BuyerSearchReqDto) (*entity.Buyer, error)

	GetLatestBuyerLogByBuyerId(id int) (entity.BuyerLog, error)
	GetDB() *gorm.DB
}

func NewBuyerRepository(db *gorm.DB) BuyerRepository {
	return &buyerRepositoryImpl{DB: db}
}
