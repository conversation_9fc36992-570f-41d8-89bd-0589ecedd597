package dto

import (
	"backend-common-lib/model"
)

type NotificationConfigDto struct {
	model.BaseDto
	UserType         *string `json:"userType"`
	NotiType         *string `json:"notiType"`
	IsSystemNoti     bool    `json:"isSystemNoti"`
	IsSmsNoti        bool    `json:"isSmsNoti"`
	IsEmailNoti      bool    `json:"isEmailNoti"`
	SystemTemplateTh *string `json:"systemTemplateTh"`
	SmsTemplateTh    *string `json:"smsTemplateTh"`
	EmailTemplateTh  *string `json:"emailTemplateTh"`
	SystemTemplateEn *string `json:"systemTemplateEn"`
	SmsTemplateEn    *string `json:"smsTemplateEn"`
	EmailTemplateEn  *string `json:"emailTemplateEn"`
}

type NotificationConfigReqDto struct {
	NotiList []*NotificationConfigDto
	ActionBy *int `json:"actionBy"`
}
