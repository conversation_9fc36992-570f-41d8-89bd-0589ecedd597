package masterbrand

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_brand"
	service "content-service/internal/service/master_brand"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewMasterBrandRepository(db)
	service := service.NewMasterBrandService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("brands")
	route.Post("/sync", h.SyncMasterBrandFromErp)
}
