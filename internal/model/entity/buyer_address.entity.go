package entity

import (
	"backend-common-lib/model"
)

type BuyerAddress struct {
	*model.BaseEntity
	AddressType              int     `gorm:"column:address_type;not null" json:"address_type"`
	HouseNumber              string  `gorm:"column:house_number;size:50" json:"house_number"`
	RoomNumber               string  `gorm:"column:room_number;size:50" json:"room_number"`
	Floor                    string  `gorm:"column:floor;size:20" json:"floor"`
	Building                 string  `gorm:"column:building;size:100" json:"building"`
	Village                  string  `gorm:"column:village;size:100" json:"village"`
	Moo                      string  `gorm:"column:moo;size:20" json:"moo"`
	Soi                      string  `gorm:"column:soi;size:100" json:"soi"`
	Road                     string  `gorm:"column:road;size:100" json:"road"`
	PostCode                 string  `gorm:"column:post_code;size:100;not null" json:"post_code"`
	SubDistrictID            int     `gorm:"column:sub_district_id;not null" json:"sub_district_id"`
	DistrictID               int     `gorm:"column:district_id;not null" json:"district_id"`
	ProvinceID               int     `gorm:"column:province_id;not null" json:"province_id"`
	CountryID                int     `gorm:"column:country_id;not null" json:"country_id"`
	BuyerID                  int     `gorm:"column:buyer_id;not null" json:"buyer_id"`
	IsActive                 bool    `gorm:"column:is_active;not null" json:"is_active"`
	IsDeleted                bool    `gorm:"column:is_deleted;not null" json:"is_deleted"`
	SubDistrictDescriptionTh *string `gorm:"column:sub_district_description_th;->" json:"subDistrictDescriptionTh"`
	SubDistrictDescriptionEn *string `gorm:"column:sub_district_description_en;->" json:"subDistrictDescriptionEn"`
	DistrictDescriptionTh    *string `gorm:"column:district_description_th;->" json:"districtDescriptionTh"`
	DistrictDescriptionEn    *string `gorm:"column:district_description_en;->" json:"districtDescriptionEn"`
	ProvinceDescriptionTh    *string `gorm:"column:province_description_th;->" json:"provinceDescriptionTh"`
	ProvinceDescriptionEn    *string `gorm:"column:province_description_en;->" json:"provinceDescriptionEn"`
	CountryDescriptionTh     *string `gorm:"column:country_description_th;->" json:"countryDescriptionTh"`
	CountryDescriptionEn     *string `gorm:"column:country_description_en;->" json:"countryDescriptionEn"`
}

func (BuyerAddress) TableName() string {
	return "buyer_address"
}

type BuyerAddressLog struct {
	*BuyerAddress
	BuyerAddressId int    `gorm:"column:buyer_address_id;not null" json:"buyer_address_id"`
	Action         string `gorm:"column:action;size:20;not null" json:"action"` // INSERT, UPDATE, DELETE
}

func (BuyerAddressLog) TableName() string {
	return "buyer_address_log"
}
