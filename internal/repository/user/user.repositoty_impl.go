package repository

import (
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

func (r *userRepositoryImpl) GetById(id int) (entity.Users, error) {
	var user entity.Users
	err := r.DB.First(&user, id).Error
	return user, err
}

func (r *userRepositoryImpl) UpdateStatus(userId int, fields map[string]interface{}) error {
	err := r.DB.Model(&entity.Users{}).Where("id = ?", userId).Updates(fields).Error
	return err
}

func (r *userRepositoryImpl) UpdateStatusTx(tx *gorm.DB, userId int, fields map[string]interface{}) error {
	err := tx.Model(&entity.Users{}).Where("id = ?", userId).Updates(fields).Error
	return err
}

func (r *userRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
