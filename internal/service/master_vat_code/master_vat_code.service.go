package service

import (
	"content-service/internal/global"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/master_vat_code"
)

type masterVatCodeService struct {
	Repo repository.MasterVatCodeRepository
}

type MasterVatCodeService interface {
	SearchMasterVatCodeFilter(req dto.MasterVatCodePageReqDto) (dto.MasterVatCodePageRespDto[dto.MasterVatCodeDto], error)
	UpdateMasterVatCodeStatus(req dto.MasterVatCodeUpdateReqDto) error
	SyncMasterVatCodeFromErp(actionBy *int, erpConfig global.ErpConfig) error
}

func NewMasterVatCodeService(repo repository.MasterVatCodeRepository) MasterVatCodeService {
	return &masterVatCodeService{Repo: repo}
}
