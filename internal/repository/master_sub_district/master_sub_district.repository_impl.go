package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (r *masterSubDistrictRepositoryImpl) buildMasterSubDistrictQuery(req dto.MasterSubDistrictPageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.MasterSubDistrict{})
	query = util.JoinUsers("master_sub_district")(query)
	query = util.ApplyFiltersFromStruct(query, req)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_sub_district")
	}
	return query
}

func (r *masterSubDistrictRepositoryImpl) FindMasterSubDistrictWithFilter(req dto.MasterSubDistrictPageReqDto) ([]entity.MasterSubDistrict, error) {
	var results []entity.MasterSubDistrict

	query := r.buildMasterSubDistrictQuery(req)
	query.Order("region_code asc, city_code asc, district_code asc, sub_district_code asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *masterSubDistrictRepositoryImpl) CountMasterSubDistrictWithFilter(req dto.MasterSubDistrictPageReqDto) (int64, error) {
	var count int64
	query := r.buildMasterSubDistrictQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *masterSubDistrictRepositoryImpl) FindMasterSubDistrictLatestSyncDate() (*time.Time, error) {
	var result entity.MasterSubDistrict
	err := r.DB.
		Model(&entity.MasterSubDistrict{}).
		Select("latest_sync_date").
		Where("latest_sync_date IS NOT NULL").
		Order("latest_sync_date desc").
		Limit(1).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return result.LatestSyncDate, nil
}

func (r *masterSubDistrictRepositoryImpl) UpdatesMasterSubDistrictFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.MasterSubDistrict{}).Where(whereClause, args...).Updates(fields)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *masterSubDistrictRepositoryImpl) FindMasterSubDistrictAll() ([]entity.MasterSubDistrict, error) {
	var result []entity.MasterSubDistrict
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterSubDistrictRepositoryImpl) UpdateMasterSubDistrictAllFields(e *entity.MasterSubDistrict) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterSubDistrictRepositoryImpl) InsertMasterSubDistrictListWithBatches(data []entity.MasterSubDistrict) error {
	if err := r.DB.CreateInBatches(&data, 500).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterSubDistrictRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}

func (r *masterSubDistrictRepositoryImpl) GetMasterSubDistrictByPostCodeAndDistrictID(postCode *string, districtID int) ([]entity.MasterSubDistrict, error) {

	var results []entity.MasterSubDistrict

	query := r.DB.Table("master_city mc").
		Select(`
			msd.id,
			msd.region_code,
			msd.city_code,
			msd.district_code,
			msd.sub_district_code,
			msd.description_th,
			msd.description_en,
			msd.is_active,
			msd.is_deleted_by_erp,
			msd.latest_sync_date
		`).
		Joins("LEFT JOIN master_district md ON mc.city_code = md.city_code").
		Joins("LEFT JOIN master_sub_district msd ON md.district_code = msd.district_code").
		Joins("LEFT JOIN master_post_code mpc ON msd.sub_district_code = mpc.sub_district_code").
		Where("md.id = ?", districtID).
		Where("msd.id is not null").
		Where("msd.is_active = true").
		Where("msd.is_deleted_by_erp = false").
		Distinct()

	if postCode != nil && *postCode != "" {
		query = query.Where("mpc.post_code = ?", *postCode)
	}

	err := query.Scan(&results).Error

	if err != nil {
		return nil, err
	}

	return results, nil
}
