package entity

import (
	"backend-common-lib/model"
	"time"
)

type MasterBrand struct {
	*model.BaseEntity
	BrandCode      *string                `column:"brand_code" json:"brandCode"`
	Description    *string                `column:"description" json:"description"`
	AssetTypeCode  *string                `column:"asset_type_code" json:"assetTypeCode"`
	IsActive       bool                   `column:"is_active" json:"isActive"`
	CompanyCode    *string                `column:"company_code" json:"companyCode"`
	IsDeletedByErp bool                   `column:"is_deleted_by_erp" json:"isDeletedByErp"`
	LatestSyncDate *time.Time             `column:"latest_sync_date" json:"latestSyncDate"`
	CreatedUser    *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:UserID"`
	UpdatedUser    *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:UserID"`
}

func (MasterBrand) TableName() string {
	return "master_brand"
}
