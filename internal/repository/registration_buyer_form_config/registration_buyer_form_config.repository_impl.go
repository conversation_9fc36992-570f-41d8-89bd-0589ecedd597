package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/entity"
)

func (r *registrationBuyerFormConfigRepositoryImpl) FindRegistrationBuyerFormConfig() (*entity.RegistrationBuyerFormConfig, error) {
	var result *entity.RegistrationBuyerFormConfig

	query := r.DB.Model(&entity.RegistrationBuyerFormConfig{})
	query = util.JoinUsers("registration_buyer_form_config")(query)

	if err := query.First(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *registrationBuyerFormConfigRepositoryImpl) UpdatesRegistrationBuyerFormConfigFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.RegistrationBuyerFormConfig{}).Where(whereClause, args...).Updates(fields)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}
