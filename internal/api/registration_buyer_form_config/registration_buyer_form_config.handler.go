package registrationbuyerformconfig

import (
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	"content-service/internal/model/dto"
	service "content-service/internal/service/registration_buyer_form_config"
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"
)

type Handler struct {
	Service   service.RegistrationBuyerFormConfigService
	ErpConfig global.ErpConfig
}

func (h *Handler) GetRegistrationBuyerFormConfig(c *fiber.Ctx) error {
	res, err := h.Service.GetRegistrationBuyerFormConfig()
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) UpdateRegistrationBuyerFormConfig(c *fiber.Ctx) error {
	var req dto.RegistrationBuyerFormConfigUpdateReqDto

	if err := c.BodyP<PERSON>er(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	idStr := c.Params("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}
	req.Id = id

	actionBy := util.GetActionByFromHeader(c)
	req.ActionBy = actionBy

	err = h.Service.UpdateRegistrationBuyerFormConfig(req)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}
