package notificationconfig

import (
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/constant"
	"content-service/internal/global"
	"content-service/internal/model/dto"
	service "content-service/internal/service/notification_config"
	"net/http"
	"strings"

	"github.com/gofiber/fiber/v2"
)

type Handler struct {
	Service   service.NotificationConfigService
	ErpConfig global.ErpConfig
}

func (h *Handler) GetNotificationConfig(c *fiber.Ctx) error {

	path := c.Path()
	userType := ""
	if strings.Contains(path, "/seller-setting/") {
		userType = constant.USER_TYPE_SELLER
	} else if strings.Contains(path, "/buyer-setting/") {
		userType = constant.USER_TYPE_BUYER
	}

	res, err := h.Service.GetNotificationConfig(userType)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *<PERSON><PERSON>) UpdateNotificationConfig(c *fiber.Ctx) error {
	var req dto.NotificationConfigReqDto
	var notiList []*dto.NotificationConfigDto
	path := c.Path()
	userType := ""
	if strings.Contains(path, "/seller-setting/") {
		userType = constant.USER_TYPE_SELLER
	} else if strings.Contains(path, "/buyer-setting/") {
		userType = constant.USER_TYPE_BUYER
	}

	if err := c.BodyParser(&notiList); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}
	req.NotiList = notiList

	actionBy := util.GetActionByFromHeader(c)
	req.ActionBy = actionBy

	err := h.Service.UpdateNotificationConfig(req, userType)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}
