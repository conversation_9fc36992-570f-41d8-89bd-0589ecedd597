package entity

import (
	"backend-common-lib/model"

	"time"

	"gorm.io/gorm"
)

type UserBidderNumbers struct {
	*model.BaseEntity
	UserId       *int            `column:"user_id" json:"userId"`
	BidderNumber *string         `column:"bidder_number" json:"bidderNumber"`
	AuctionDate  *time.Time      `column:"auction_date" json:"auctionDate"`
	DeletedDate  *gorm.DeletedAt `gorm:"column:deleted_date" json:"deletedDate"`

	BuyerFirstName  *string `gorm:"column:buyer_first_name;->" json:"buyerFirstName"`
	BuyerMiddleName *string `gorm:"column:buyer_middle_name;->" json:"buyerMiddleName"`
	BuyerLastName   *string `gorm:"column:buyer_last_name;->" json:"buyerLastName"`
	PrefixName      *string `gorm:"column:prefix_name;->" json:"prefixName"`
	LotId           *int    `gorm:"column:lot_id;->" json:"lotId"`
}

func (UserBidderNumbers) TableName() string {
	return "user_bidder_numbers"
}
