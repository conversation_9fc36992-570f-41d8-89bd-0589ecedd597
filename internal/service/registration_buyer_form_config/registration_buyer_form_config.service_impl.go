package service

import (
	"backend-common-lib/errs"
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"fmt"
	"net/http"

	"backend-common-lib/constant"

	"github.com/gofiber/fiber/v2"
)

func (s *registrationBuyerFormConfigService) GetRegistrationBuyerFormConfig() (dto.RegistrationBuyerFormConfigRespDto, error) {
	resp := dto.RegistrationBuyerFormConfigRespDto{}
	registrationBuyerFormConfig, err := s.Repo.FindRegistrationBuyerFormConfig()
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - map to dto
	resp = util.MapToWithCreatedByAndUpdatedBy[dto.RegistrationBuyerFormConfigRespDto](registrationBuyerFormConfig)

	return resp, nil
}

func (s *registrationBuyerFormConfigService) UpdateRegistrationBuyerFormConfig(req dto.RegistrationBuyerFormConfigUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_email_require":    req.IsEmailRequire,
		"is_show_finance_tab": req.IsShowFinanceTab,
		"updated_by":          req.ActionBy,
		"updated_date":        util.Now(),
	}

	affectedRows, err := s.Repo.UpdatesRegistrationBuyerFormConfigFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}
