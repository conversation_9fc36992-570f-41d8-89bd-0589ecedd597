package dto

import (
	"backend-common-lib/model"
)

type BuyerEditProfileSearchReqDto struct {
	Search         *string `json:"search"`
	NationalityId  *int    `json:"nationalityId"`
	ApprovalStatus *string `json:"approvalStatus"`
	model.PagingRequest
}

type BuyerEditProfileSearchRespDto struct {
	BuyerId                   *int    `json:"buyerId"`
	BuyerEditProfileRequestId *int    `json:"buyerEditProfileRequestId"`
	CreatedDate               *string `json:"createdDate"`
	BuyerEditProfileSearchChangeDto
	Status *string `json:"status"`
	Remark *string `json:"remark"`
}

type BuyerEditProfileSearchDto struct {
	BidderId             *string `json:"bidderId"`
	Username             *string `json:"username"`
	Email                *string `json:"email"`
	PhoneNumber          *string `json:"phoneNumber"`
	PrefixNameTh         *string `json:"prefixNameTh"`
	PrefixNameEn         *string `json:"prefixNameEn"`
	FirstName            *string `json:"firstName"`
	MiddleName           *string `json:"middleName"`
	LastName             *string `json:"lastName"`
	TaxId                *string `json:"taxId"`
	IdentificationNumber *string `json:"identificationNumber"`
	NationalityTh        *string `json:"nationalityTh"`
	NationalityEn        *string `json:"nationalityEn"`
	DateOfBirthStr       *string `json:"dateOfBirth"`
	AddressTh            *string `json:"addressTh"`
	AddressEn            *string `json:"addressEn"`
	Status               *string `json:"status"`
	Remark               *string `json:"remark"`
}

type BuyerEditProfileSearchChangeDto struct {
	BidderId             *ChangeValueDto `json:"bidderId"`
	Username             *ChangeValueDto `json:"username"`
	Email                *ChangeValueDto `json:"email"`
	PhoneNumber          *ChangeValueDto `json:"phoneNumber"`
	PrefixNameTh         *ChangeValueDto `json:"prefixNameTh"`
	PrefixNameEn         *ChangeValueDto `json:"prefixNameEn"`
	FirstName            *ChangeValueDto `json:"firstName"`
	MiddleName           *ChangeValueDto `json:"middleName"`
	LastName             *ChangeValueDto `json:"lastName"`
	IdentificationNumber *ChangeValueDto `json:"identificationNumber"`
	NationalityTh        *ChangeValueDto `json:"nationalityTh"`
	NationalityEn        *ChangeValueDto `json:"nationalityEn"`
	DateOfBirthStr       *ChangeValueDto `json:"dateOfBirth"`
	AddressTh            *ChangeValueDto `json:"addressTh"`
	AddressEn            *ChangeValueDto `json:"addressEn"`
}

type ChangeValueDto struct {
	FieldName *string     `json:"fieldName"`
	IsEdit    bool        `json:"isEdit"`
	OldValue  interface{} `json:"oldValue"`
	NewValue  interface{} `json:"newValue"`
}

type BuyerEditProfilePageRespDto[T any] struct {
	model.PagingModel[T]
}

type BuyerEditProfileAddressDto struct {
	HouseNumber              string  `json:"houseNumber"`
	RoomNumber               string  `json:"roomNumber"`
	Floor                    string  `json:"floor"`
	Building                 string  `json:"building"`
	Village                  string  `json:"village"`
	Moo                      string  `json:"moo"`
	Soi                      string  `json:"soi"`
	Road                     string  `json:"road"`
	PostCode                 string  `json:"postCode"`
	SubDistrictID            int     `json:"subDistrictId"`
	DistrictID               int     `json:"districtId"`
	ProvinceID               int     `json:"provinceId"`
	CountryID                int     `json:"countryId"`
	SubDistrictDescriptionTh *string `json:"subDistrictDescriptionTh"`
	SubDistrictDescriptionEn *string `json:"subDistrictDescriptionEn"`
	DistrictDescriptionTh    *string `json:"districtDescriptionTh"`
	DistrictDescriptionEn    *string `json:"districtDescriptionEn"`
	ProvinceDescriptionTh    *string `json:"provinceDescriptionTh"`
	ProvinceDescriptionEn    *string `json:"provinceDescriptionEn"`
	CountryDescriptionTh     *string `json:"countryDescriptionTh"`
	CountryDescriptionEn     *string `json:"countryDescriptionEn"`
}

type UpdateBuyerEditProfileStatusReqDto struct {
	model.BaseDtoActionBy
	ApprovalStatus *string `json:"approvalStatus"`
	Remark         *string `json:"remark"`
}

type BuyerEditProfileViewDto struct {
	BuyerId                   *int                           `json:"buyerId"`
	BuyerEditProfileRequestId *int                           `json:"buyerEditProfileRequestId"`
	OldBuyerEditProfileDto    *BuyerEditProfileDto           `json:"oldBuyerEditProfileDto"`
	NewBuyerEditProfileDto    *BuyerEditProfileViewChangeDto `json:"newBuyerEditProfileDto"`
}

type BuyerEditProfileDto struct {
	BidderId             *string `json:"bidderId"`
	Username             *string `json:"username"`
	Email                *string `json:"email"`
	PhoneNumber          string  `json:"phoneNumber"`
	TaxId                *string `json:"taxId"`
	IdentificationNumber *string `json:"identificationNumber"`
	NationalityId        *int    `json:"nationalityId"`
	NationalityTh        *string `json:"nationalityTh"`
	NationalityEn        *string `json:"nationalityEn"`
	DateOfBirthStr       *string `json:"dateOfBirth"`
	PrefixNameId         int     `json:"prefixNameId"`
	PrefixNameTh         *string `json:"prefixNameTh"`
	PrefixNameEn         *string `json:"prefixNameEn"`
	FirstName            *string `json:"firstName"`
	MiddleName           *string `json:"middleName"`
	LastName             *string `json:"lastName"`
	*BuyerEditProfileAddressDto
}

type BuyerEditProfileViewChangeDto struct {
	*BuyerEditProfileChangeDto
	*BuyerEditProfileAddressChangeDto
}

type BuyerEditProfileChangeDto struct {
	BidderId             *ChangeValueDto `json:"bidderId"`
	Username             *ChangeValueDto `json:"username"`
	Email                *ChangeValueDto `json:"email"`
	PhoneNumber          *ChangeValueDto `json:"phoneNumber"`
	TaxId                *ChangeValueDto `json:"taxId"`
	IdentificationNumber *ChangeValueDto `json:"identificationNumber"`
	NationalityId        *ChangeValueDto `json:"nationalityId"`
	NationalityTh        *ChangeValueDto `json:"nationalityTh"`
	NationalityEn        *ChangeValueDto `json:"nationalityEn"`
	DateOfBirthStr       *ChangeValueDto `json:"dateOfBirth"`
	PrefixNameId         *ChangeValueDto `json:"prefixNameId"`
	PrefixNameTh         *ChangeValueDto `json:"prefixNameTh"`
	PrefixNameEn         *ChangeValueDto `json:"prefixNameEn"`
	FirstName            *ChangeValueDto `json:"firstName"`
	MiddleName           *ChangeValueDto `json:"middleName"`
	LastName             *ChangeValueDto `json:"lastName"`
}

type BuyerEditProfileAddressChangeDto struct {
	HouseNumber              *ChangeValueDto `json:"houseNumber"`
	RoomNumber               *ChangeValueDto `json:"roomNumber"`
	Floor                    *ChangeValueDto `json:"floor"`
	Building                 *ChangeValueDto `json:"building"`
	Village                  *ChangeValueDto `json:"village"`
	Moo                      *ChangeValueDto `json:"moo"`
	Soi                      *ChangeValueDto `json:"soi"`
	Road                     *ChangeValueDto `json:"road"`
	PostCode                 *ChangeValueDto `json:"postCode"`
	SubDistrictID            *ChangeValueDto `json:"subDistrictId"`
	DistrictID               *ChangeValueDto `json:"districtId"`
	ProvinceID               *ChangeValueDto `json:"provinceId"`
	CountryID                *ChangeValueDto `json:"countryId"`
	SubDistrictDescriptionTh *ChangeValueDto `json:"subDistrictDescriptionTh"`
	SubDistrictDescriptionEn *ChangeValueDto `json:"subDistrictDescriptionEn"`
	DistrictDescriptionTh    *ChangeValueDto `json:"districtDescriptionTh"`
	DistrictDescriptionEn    *ChangeValueDto `json:"districtDescriptionEn"`
	ProvinceDescriptionTh    *ChangeValueDto `json:"provinceDescriptionTh"`
	ProvinceDescriptionEn    *ChangeValueDto `json:"provinceDescriptionEn"`
	CountryDescriptionTh     *ChangeValueDto `json:"countryDescriptionTh"`
	CountryDescriptionEn     *ChangeValueDto `json:"countryDescriptionEn"`
}

type NewBuyerEditProfileDto struct {
	*BuyerEditProfileDto
	NewChange *BuyerEditProfileChangeDto `json:"newChange"`
}
