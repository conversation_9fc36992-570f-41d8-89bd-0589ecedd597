package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type masterCityRepositoryImpl struct {
	DB *gorm.DB
}

type MasterCityRepository interface {
	FindMasterCityWithFilter(req dto.MasterCityPageReqDto) ([]entity.MasterCity, error)
	CountMasterCityWithFilter(req dto.MasterCityPageReqDto) (int64, error)
	FindMasterCityLatestSyncDate() (*time.Time, error)
	UpdatesMasterCityFieldsWhere(field map[string]interface{}, whereClause string, args ...interface{}) (int64, error)
	FindMasterCityAll() ([]entity.MasterCity, error)
	UpdateMasterCityAllFields(e *entity.MasterCity) error
	InsertMasterCityList(data []entity.MasterCity) error
	GetProvinceByPostCode(postCode string) ([]entity.MasterCity, error)

	GetDB() *gorm.DB
}

func NewMasterCityRepository(db *gorm.DB) MasterCityRepository {
	return &masterCityRepositoryImpl{DB: db}
}
