package entity

import (
	"backend-common-lib/model"
	"time"
)

type Buyer struct {
	*model.BaseEntity
	KeyPwd                  *string                     `gorm:"column:key_pwd;->" json:"key_pwd"`
	PasswordHash            *string                     `gorm:"column:password_hash;->" json:"password_hash"`
	RoleID                  *int                        `gorm:"column:role_id;->" json:"role_id"`
	FirstName               *string                     `column:"first_name" json:"firstname"`
	LastName                *string                     `column:"last_name" json:"lastname"`
	BidderId                *string                     `column:"bidder_id" json:"bidder_id"`
	LoginFailCount          *int                        `gorm:"column:login_fail_count;->" json:"login_fail_count"`
	LatestLogin             *time.Time                  `gorm:"column:latest_login;->" json:"latest_login" example:"2020-01-01"`
	CustomerGroupId         *int                        `column:"customer_group_id" json:"customerGroupId"`
	CustomerGroupDesc       *string                     `gorm:"column:customer_group_desc;->" json:"customerGroupDesc"`
	PrefixNameId            int                         `column:"prefix_name_id" json:"prefixNameId"`
	MiddleName              *string                     `column:"middle_name" json:"middleName"`
	FullName                *string                     `gorm:"column:full_name;->" json:"fullName"`
	PhoneNumber             *string                     `column:"phone_number" json:"phoneNumber"`
	TaxId                   *string                     `gorm:"column:tax_id;->" json:"taxId"`
	PaddleNo                *string                     `gorm:"column:paddle_no;->" json:"paddleNo"`
	RegisChannel            *string                     `column:"regis_channel" json:"regisChannel"`
	GenerateLabel           *string                     `gorm:"column:generate_label;->" json:"generateLabel"`
	IsActive                bool                        `gorm:"column:is_active;->" json:"isActive"`
	IsApproved              bool                        `column:"is_approved" json:"isApproved"`
	IsBlock                 bool                        `column:"is_block" json:"isBlock"`
	IsBlacklist             bool                        `column:"is_blacklist" json:"reasonBlockBlacklist"`
	ReasonBlockBlacklist    *string                     `column:"reason_block_blacklist" json:"reason"`
	Email                   *string                     `column:"email" json:"email"`
	AccountType             *int                        `column:"account_type" json:"accountType"`
	IdentificationNumber    *string                     `column:"identification_number" json:"identificationNumber"`
	DateOfBirth             *time.Time                  `column:"date_of_birth" json:"dateOfBirth"`
	DateOfExpiry            *time.Time                  `column:"date_of_expiry" json:"dateOfExpiry"`
	Nationality             *string                     `column:"nationality" json:"nationality"`
	PhoneCountryCode        *string                     `column:"phone_country_code" json:"phoneCountryCode"`
	MainBidderId            *string                     `column:"phone_country_code" json:"mainBidderId"`
	IsSyncErp               *bool                       `column:"main_bidder_id" json:"isSyncErp"`
	LatestSyncDate          *time.Time                  `column:"latest_sync_date" json:"latestSyncDate"`
	LatestSyncBy            *int                        `column:"latest_sync_by" json:"latestSyncBy"`
	UserId                  *int                        `column:"user_id" json:"userId"`
	CustomerNo              *string                     `column:"customer_no" json:"customerNo"`
	CompanyBusinessType     *string                     `column:"company_business_type" json:"companyBusinessType"`
	CompanyName             *string                     `column:"company_name" json:"companyName"`
	CompanyRegistrationDate *time.Time                  `column:"company_registration_date" json:"companyRegistrationDate"`
	CompanyBranchCode       *string                     `column:"company_branch_code" json:"companyBranchCode"`
	NationalityId           *int                        `column:"nationality_id" json:"nationalityId"`
	CustomerGroupForJoin    *model.CustomerGroupForJoin `gorm:"foreignKey:CustomerGroupId;references:ID"`
	Prefix                  *string                     `gorm:"column:prefix;->" json:"prefix"`
	PrefixNameTh            *string                     `gorm:"column:prefix_name_th;->" json:"prefixNameTh"`
	PrefixNameEn            *string                     `gorm:"column:prefix_name_en;->" json:"prefixNameEn"`
	Username                *string                     `gorm:"column:username;->" json:"username"`
	NationalityTh           *string                     `gorm:"column:nationality_th;->" json:"nationalityTh"`
	NationalityEn           *string                     `gorm:"column:nationality_en;->" json:"nationalityEn"`
	BidderNumber            *string                     `gorm:"column:bidder_number;->" json:"bidderNumber"`
	AccountStatusCode       *string                     `gorm:"column:account_status_code;->" json:"accountStatusCode"`
	UserStatusCode          *string                     `gorm:"column:user_status_code;->" json:"userStatusCode"`
	UserIsActive            *bool                       `gorm:"column:user_is_active;->" json:"userIsActive"`
}

func (Buyer) TableName() string {
	return "buyer"
}

type BuyerLog struct {
	*Buyer
	BuyerId int    `gorm:"column:buyer_id;not null" json:"buyer_id"`
	Action  string `gorm:"column:action;size:20;not null" json:"action"` // INSERT, UPDATE, DELETE
}

func (BuyerLog) TableName() string {
	return "buyer_log"
}

type BuyerForJoin struct {
	ID       int     `gorm:"primaryKey" column:"id" json:"id"`
	BidderId *string `column:"bidder_id" json:"bidderId" example:"1"`
	UserId   int     `column:"user_id" json:"userId"`
}

func (BuyerForJoin) TableName() string {
	return "buyer"
}
