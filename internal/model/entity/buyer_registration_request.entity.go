package entity

import (
	constants "backend-common-lib/constants/registration_approval_status"
	"backend-common-lib/model"
	"time"
)

type BuyerRegistrationRequest struct {
	*model.BaseEntity
	BuyerID        int                          `gorm:"column:buyer_id;not null" json:"buyerId"`
	BuyerLogID     *int                         `gorm:"column:buyer_log_id" json:"buyerLogId"`
	IsActive       bool                         `gorm:"column:is_active;not null" json:"isActive"`
	IsDeleted      bool                         `gorm:"column:is_deleted;not null" json:"isDeleted"`
	RequestDate    *time.Time                   `gorm:"column:request_date" json:"requestDate"`
	ApprovalStatus constants.ApprovalStatusEnum `gorm:"column:approval_status;type:approval_status_enum;default:PENDING;not null" json:"approvalStatus"`
	Remark         *string                      `gorm:"column:remark" json:"remark"`

	// File information - ignored during insert/update to main table
	IdIdCardFile         *string `gorm:"column:id_id_card_file;<-:false" json:"idIdCardFile"`
	IdCardFile           *string `gorm:"column:id_card_file;<-:false" json:"idCardFile"`
	IdCardFileType       *string `gorm:"column:id_card_file_type;<-:false" json:"idCardFileType"`
	IdPermitDocFile      *string `gorm:"column:id_permit_doc_file;<-:false" json:"idPermitDocFile"`
	PermitDocFile        *string `gorm:"column:permit_doc_file;<-:false" json:"permitDocFile"`
	PermitDocFileType    *string `gorm:"column:permit_doc_file_type;<-:false" json:"permitFileType"`
	IdBankAccountFile    *string `gorm:"column:id_bank_account_file;<-:false" json:"idBankAccountFile"`
	BankAccountFile      *string `gorm:"column:bank_account_file;<-:false" json:"bankAccountFile"`
	BankAccountFileType  *string `gorm:"column:bank_account_file_type;<-:false" json:"bankAccountFileType"`
	IdOtherAccountFile   *string `gorm:"column:id_other_account_file;<-:false" json:"idOtherAccountFile"`
	OtherAccountFile     *string `gorm:"column:other_account_file;<-:false" json:"otherAccountFile"`
	OtherAccountFileType *string `gorm:"column:other_account_file_type;<-:false" json:"otherAccountFileType"`

	// Customer type information - ignored during insert/update to main table
	CustomerTypeTh   *string `gorm:"column:customer_type_th;<-:false" json:"customerTypeTh"`
	CustomerTypeEn   *string `gorm:"column:customer_type_en;<-:false" json:"customerTypeEn"`
	CustomerTypeCode *string `gorm:"column:customer_type_code;<-:false" json:"customerTypeCode"`
	CustomerTypeId   *int    `gorm:"column:customer_type_id;<-:false" json:"customerTypeId"`

	// Buyer basic information - ignored during insert/update to main table
	PrefixNameID         int        `gorm:"column:prefix_name_id;<-:false" json:"prefixNameId"`
	PrefixTh             *string    `gorm:"column:prefix_th;<-:false" json:"prefixTh"`
	PrefixEn             *string    `gorm:"column:prefix_en;<-:false" json:"prefixEn"`
	FirstName            *string    `gorm:"column:first_name;<-:false" json:"firstName"`
	MiddleName           *string    `gorm:"column:middle_name;<-:false" json:"middleName"`
	LastName             *string    `gorm:"column:last_name;<-:false" json:"lastName"`
	DateOfBirth          *time.Time `gorm:"column:date_of_birth;<-:false" json:"dateOfBirth"`
	NationalityTh        *string    `gorm:"column:nationality_th;<-:false" json:"nationalityTh"`
	NationalityEn        *string    `gorm:"column:nationality_en;<-:false" json:"nationalityEn"`
	NationalityCode      *string    `gorm:"column:nationality_code;<-:false" json:"nationalityCode"`
	NationalityId        *int       `gorm:"column:nationality_id;<-:false" json:"nationalityId"`
	IdentificationNumber *string    `gorm:"column:identification_number;<-:false" json:"identificationNumber"`
	DateOfExpiry         *time.Time `gorm:"column:date_of_expiry;<-:false" json:"dateOfExpiry"`
	PhoneNumber          *string    `gorm:"column:phone_number;<-:false" json:"phoneNumber"`
	Email                *string    `gorm:"column:email;<-:false" json:"email"`

	// Company information - ignored during insert/update to main table
	CompanyName             *string    `gorm:"column:company_name;<-:false" json:"companyName"`
	CompanyRegistrationDate *time.Time `gorm:"column:company_registration_date;<-:false" json:"companyRegistrationDate"`
	CompanyBusinessType     *string    `gorm:"column:company_business_type;<-:false" json:"companyBusinessType"`
	CompanyBranchCode       *string    `gorm:"column:company_branch_code;<-:false" json:"companyBranchCode"`

	// Director information - ignored during insert/update to main table
	DirectorId                   *int    `gorm:"column:director_id;<-:false" json:"directorId"`
	DirectorPrefixTh             *string `gorm:"column:director_prefix_th;<-:false" json:"directorPrefixTh"`
	DirectorPrefixEn             *string `gorm:"column:director_prefix_en;<-:false" json:"directorPrefixEn"`
	DirectorFirstName            *string `gorm:"column:director_first_name;<-:false" json:"directorFirstName"`
	DirectorMiddleName           *string `gorm:"column:director_middle_name;<-:false" json:"directorMiddleName"`
	DirectorLastName             *string `gorm:"column:director_last_name;<-:false" json:"directorLastName"`
	DirectorIdentificationNumber *string `gorm:"column:director_identification_number;<-:false" json:"directorIdentificationNumber"`
	DirectorFileName             *string `gorm:"column:director_file_name;<-:false" json:"directorFileName"`
	DirectorFileType             *string `gorm:"column:director_file_type;<-:false" json:"directorFileType"`
	DirectorFileCategory         *string `gorm:"column:director_file_category;<-:false" json:"directorFileCategory"`

	// Registered Address (TYPE 1) - ignored during insert/update to main table
	RegAddressTypeID           int     `gorm:"column:reg_address_type_id;<-:false" json:"regAddressTypeId"`
	RegHouseNumber             *string `gorm:"column:reg_house_number;<-:false" json:"regHouseNumber"`
	RegMoo                     *string `gorm:"column:reg_moo;<-:false" json:"regMoo"`
	RegVillage                 *string `gorm:"column:reg_village;<-:false" json:"regVillage"`
	RegRoomNumber              *string `gorm:"column:reg_room_number;<-:false" json:"regRoomNumber"`
	RegFloor                   *string `gorm:"column:reg_floor;<-:false" json:"regFloor"`
	RegBuilding                *string `gorm:"column:reg_building;<-:false" json:"regBuilding"`
	RegSoi                     *string `gorm:"column:reg_soi;<-:false" json:"regSoi"`
	RegRoad                    *string `gorm:"column:reg_road;<-:false" json:"regRoad"`
	RegPostCode                *int    `gorm:"column:reg_post_code;<-:false" json:"regPostCode"`
	RegCountryCode             *string `gorm:"column:reg_country_code;<-:false" json:"regCountryCode"`
	RegMasterSubDistrictCode   *string `gorm:"column:reg_master_sub_district_code;<-:false" json:"regMasterSubDistrictCode"`
	RegSubDistrictTh           *string `gorm:"column:reg_sub_district_th;<-:false" json:"regSubDistrictTh"`
	RegSubDistrictEn           *string `gorm:"column:reg_sub_district_en;<-:false" json:"regSubDistrictEn"`
	RegDistrictTh              *string `gorm:"column:reg_district_th;<-:false" json:"regDistrictTh"`
	RegDistrictEn              *string `gorm:"column:reg_district_en;<-:false" json:"regDistrictEn"`
	RegMasterCityDescriptionTh *string `gorm:"column:reg_master_city_description_th;<-:false" json:"regMasterCityDescriptionTh"`
	RegMasterCityDescriptionEn *string `gorm:"column:reg_master_city_description_en;<-:false" json:"regMasterCityDescriptionEn"`

	// Document Address (TYPE 2) - ignored during insert/update to main table
	DocAddressTypeID           int     `gorm:"column:doc_address_type_id;<-:false" json:"docAddressTypeId"`
	DocHouseNumber             *string `gorm:"column:doc_house_number;<-:false" json:"docHouseNumber"`
	DocMoo                     *string `gorm:"column:doc_moo;<-:false" json:"docMoo"`
	DocVillage                 *string `gorm:"column:doc_village;<-:false" json:"docVillage"`
	DocRoomNumber              *string `gorm:"column:doc_room_number;<-:false" json:"docRoomNumber"`
	DocFloor                   *string `gorm:"column:doc_floor;<-:false" json:"docFloor"`
	DocBuilding                *string `gorm:"column:doc_building;<-:false" json:"docBuilding"`
	DocSoi                     *string `gorm:"column:doc_soi;<-:false" json:"docSoi"`
	DocRoad                    *string `gorm:"column:doc_road;<-:false" json:"docRoad"`
	DocPostCode                *int    `gorm:"column:doc_post_code;<-:false" json:"docPostCode"`
	DocCountryCode             *string `gorm:"column:doc_country_code;<-:false" json:"docCountryCode"`
	DocMasterSubDistrictCode   *string `gorm:"column:doc_master_sub_district_code;<-:false" json:"docMasterSubDistrictCode"`
	DocSubDistrictTh           *string `gorm:"column:doc_sub_district_th;<-:false" json:"docSubDistrictTh"`
	DocSubDistrictEn           *string `gorm:"column:doc_sub_district_en;<-:false" json:"docSubDistrictEn"`
	DocDistrictTh              *string `gorm:"column:doc_district_th;<-:false" json:"docDistrictTh"`
	DocDistrictEn              *string `gorm:"column:doc_district_en;<-:false" json:"docDistrictEn"`
	DocMasterCityDescriptionTh *string `gorm:"column:doc_master_city_description_th;<-:false" json:"docMasterCityDescriptionTh"`
	DocMasterCityDescriptionEn *string `gorm:"column:doc_master_city_description_en;<-:false" json:"docMasterCityDescriptionEn"`

	// Registration Book Address (TYPE 3) - ignored during insert/update to main table
	BookAddressTypeID           int     `gorm:"column:book_address_type_id;<-:false" json:"bookAddressTypeId"`
	BookHouseNumber             *string `gorm:"column:book_house_number;<-:false" json:"bookHouseNumber"`
	BookMoo                     *string `gorm:"column:book_moo;<-:false" json:"bookMoo"`
	BookVillage                 *string `gorm:"column:book_village;<-:false" json:"bookVillage"`
	BookRoomNumber              *string `gorm:"column:book_room_number;<-:false" json:"bookRoomNumber"`
	BookFloor                   *string `gorm:"column:book_floor;<-:false" json:"bookFloor"`
	BookBuilding                *string `gorm:"column:book_building;<-:false" json:"bookBuilding"`
	BookSoi                     *string `gorm:"column:book_soi;<-:false" json:"bookSoi"`
	BookRoad                    *string `gorm:"column:book_road;<-:false" json:"bookRoad"`
	BookPostCode                *int    `gorm:"column:book_post_code;<-:false" json:"bookPostCode"`
	BookCountryCode             *string `gorm:"column:book_country_code;<-:false" json:"bookCountryCode"`
	BookMasterSubDistrictCode   *string `gorm:"column:book_master_sub_district_code;<-:false" json:"bookMasterSubDistrictCode"`
	BookSubDistrictTh           *string `gorm:"column:book_sub_district_th;<-:false" json:"bookSubDistrictTh"`
	BookSubDistrictEn           *string `gorm:"column:book_sub_district_en;<-:false" json:"bookSubDistrictEn"`
	BookDistrictTh              *string `gorm:"column:book_district_th;<-:false" json:"bookDistrictTh"`
	BookDistrictEn              *string `gorm:"column:book_district_en;<-:false" json:"bookDistrictEn"`
	BookMasterCityDescriptionTh *string `gorm:"column:book_master_city_description_th;<-:false" json:"bookMasterCityDescriptionTh"`
	BookMasterCityDescriptionEn *string `gorm:"column:book_master_city_description_en;<-:false" json:"bookMasterCityDescriptionEn"`

	// Shipping Address (TYPE 4) - ignored during insert/update to main table
	ShipAddressTypeID           int     `gorm:"column:ship_address_type_id;<-:false" json:"shipAddressTypeId"`
	ShipHouseNumber             *string `gorm:"column:ship_house_number;<-:false" json:"shipHouseNumber"`
	ShipMoo                     *string `gorm:"column:ship_moo;<-:false" json:"shipMoo"`
	ShipVillage                 *string `gorm:"column:ship_village;<-:false" json:"shipVillage"`
	ShipRoomNumber              *string `gorm:"column:ship_room_number;<-:false" json:"shipRoomNumber"`
	ShipFloor                   *string `gorm:"column:ship_floor;<-:false" json:"shipFloor"`
	ShipBuilding                *string `gorm:"column:ship_building;<-:false" json:"shipBuilding"`
	ShipSoi                     *string `gorm:"column:ship_soi;<-:false" json:"shipSoi"`
	ShipRoad                    *string `gorm:"column:ship_road;<-:false" json:"shipRoad"`
	ShipPostCode                *int    `gorm:"column:ship_post_code;<-:false" json:"shipPostCode"`
	ShipCountryCode             *string `gorm:"column:ship_country_code;<-:false" json:"shipCountryCode"`
	ShipMasterSubDistrictCode   *string `gorm:"column:ship_master_sub_district_code;<-:false" json:"shipMasterSubDistrictCode"`
	ShipSubDistrictTh           *string `gorm:"column:ship_sub_district_th;<-:false" json:"shipSubDistrictTh"`
	ShipSubDistrictEn           *string `gorm:"column:ship_sub_district_en;<-:false" json:"shipSubDistrictEn"`
	ShipDistrictTh              *string `gorm:"column:ship_district_th;<-:false" json:"shipDistrictTh"`
	ShipDistrictEn              *string `gorm:"column:ship_district_en;<-:false" json:"shipDistrictEn"`
	ShipMasterCityDescriptionTh *string `gorm:"column:ship_master_city_description_th;<-:false" json:"shipMasterCityDescriptionTh"`
	ShipMasterCityDescriptionEn *string `gorm:"column:ship_master_city_description_en;<-:false" json:"shipMasterCityDescriptionEn"`

	// Bank information - ignored during insert/update to main table // Not have table
	BankAccountNumber *string `gorm:"column:bank_account_number;<-:false" json:"bankAccountNumber"` // เลชที่บัญชี
	BankName          *string `gorm:"column:bank_account_code;<-:false" json:"bankName"`            // ชื่อธนาคาร
	AccountName       *string `gorm:"column:bank_account;<-:false" json:"accountName"`              // ชื่อบัญชี
}

func (BuyerRegistrationRequest) TableName() string {
	return "buyer_registration_request"
}
