package entity

import (
	"backend-common-lib/model"
	"time"
)

type Users struct {
	*model.BaseEntity
	Username            *string    `gorm:"column:username" json:"username"`
	Email               *string    `gorm:"column:email" json:"email"`
	PasswordHash        string     `gorm:"column:password_hash" json:"passwordHash"`
	ManagerId           *int       `gorm:"column:manager_id" json:"managerId"`
	PasswordExpiredDate *time.Time `gorm:"column:password_expired_date" json:"passwordExpiredDate"`
	PinHash             *string    `gorm:"column:pin_hash" json:"pinHash"`
	PinExpiredDate      *time.Time `gorm:"column:pin_expired_date" json:"pinExpiredDate"`
	IsSetupPin          bool       `gorm:"column:is_setup_pin" json:"isSetupPin"`
	IsActive            bool       `gorm:"column:is_active" json:"isActive"`
	IsLock              bool       `gorm:"column:is_lock" json:"isLock"`
	LastLogin           *time.Time `gorm:"column:last_login" json:"lastLogin"`
	LoginFailCount      int        `gorm:"column:login_fail_count" json:"loginFailCount"`
	PinFailCount        int        `gorm:"column:pin_fail_count" json:"pinFailCount"`
	IsOnline            *bool      `gorm:"column:is_online" json:"isOnline"`
	DeletedDate         *time.Time `gorm:"column:deleted_date" json:"deletedDate"`
	DeletedBy           *int       `gorm:"column:deleted_by" json:"deletedBy"`
}

func (Users) TableName() string {
	return "users"
}
