package policyconsent

import (
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	"content-service/internal/model/dto"
	service "content-service/internal/service/policy_consent"
	"fmt"
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"
)

type Handler struct {
	Service   service.PolicyConsentService
	ErpConfig global.ErpConfig
}

func (h *Handler) GetPolicyConsent(c *fiber.Ctx) error {
	var req model.PagingRequest

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	consentType := c.Params("type")
	if consentType == "" {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("consent type is required"))
	} else {
		consentType = "seller-" + consentType
	}

	res, err := h.Service.GetPolicyConsent(req, consentType)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetPolicyConsentByID(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	res, err := h.Service.GetPolicyConsentByID(id)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) CreatePolicyConsent(c *fiber.Ctx) error {
	var req dto.PolicyConsentReqDto

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	consentType := c.Params("type")
	if consentType == "" {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("consent type is required"))
	} else {
		consentType = "seller-" + consentType
	}
	actionBy := util.GetActionByFromHeader(c)
	req.ActionBy = actionBy

	err := h.Service.CreatePolicyConsent(req, consentType)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}

func (h *Handler) UpdatePolicyConsent(c *fiber.Ctx) error {
	var req dto.PolicyConsentReqDto

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	idStr := c.Params("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}
	req.Id = id

	consentType := c.Params("type")
	if consentType == "" {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("consent type is required"))
	} else {
		consentType = "seller-" + consentType
	}
	actionBy := util.GetActionByFromHeader(c)
	req.ActionBy = actionBy

	err = h.Service.UpdatePolicyConsent(req, consentType)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}

func (h *Handler) UpdatePolicyConsentStatus(c *fiber.Ctx) error {
	var req dto.PolicyConsentReqDto

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	idStr := c.Params("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}
	req.Id = id

	actionBy := util.GetActionByFromHeader(c)
	req.ActionBy = actionBy

	err = h.Service.UpdatePolicyConsentStatus(req)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}

func (h *Handler) DeletePolicyConsent(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	err = h.Service.DeletePolicyConsent(id)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}

func (h *Handler) ValidateOverlapStartDate(c *fiber.Ctx) error {
	var req dto.PolicyConsentReqDto

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	consentType := c.Params("type")
	if consentType == "" {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("consent type is required"))
	} else {
		consentType = "seller-" + consentType
	}

	err := h.Service.ValidateOverlapStartDate(req, consentType)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}
