package service

import (
	"content-service/internal/model/dto"

	buyerRepo "content-service/internal/repository/buyer"
	repository "content-service/internal/repository/buyer_registration_request"
	mastercountryrepo "content-service/internal/repository/master_country"
)

type buyerRegistrationRequest struct {
	Repo              repository.BuyerRegistrationRequestRepository
	BuyerRepo         buyerRepo.BuyerRepository
	MasterCountryRepo mastercountryrepo.MasterCountryRepository
}

type BuyerRegistrationRequestService interface {
	SearchBuyerRegistrationRequestFilter(req dto.BuyerRegistrationRequestPageReqDto) (dto.BuyerRegistrationRequestPageRespDto[dto.BuyerRegistrationRequestDto], error)
	SearchBuyerRegistrationRequestFilterById(id int) (dto.BuyerRegistrationRequestDetailDto, error)
	UpdateBuyerRegistrationRequestStatus(req dto.BuyerRegistrationRequestUpdateReqDto) error
}

func NewBuyerRegistrationRequestService(repo repository.BuyerRegistrationRequestRepository, buyerRepo buyerRepo.BuyerRepository, masterCountryRepo mastercountryrepo.MasterCountryRepository) BuyerRegistrationRequestService {
	return &buyerRegistrationRequest{Repo: repo, BuyerRepo: buyerRepo, MasterCountryRepo: masterCountryRepo}
}
