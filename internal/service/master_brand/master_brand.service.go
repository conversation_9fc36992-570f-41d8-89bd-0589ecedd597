package service

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_brand"
)

type masterBrandService struct {
	Repo repository.MasterBrandRepository
}

type MasterBrandService interface {
	SyncMasterBrandFromErp(ActionBy *int, erpConfig global.ErpConfig) error
}

func NewMasterBrandService(repo repository.MasterBrandRepository) MasterBrandService {
	return &masterBrandService{Repo: repo}
}
