package entity

import (
	"backend-common-lib/model"
	"time"
)

type MasterAssetLocation struct {
	*model.BaseEntity
	LocationCode   *string                `column:"location_code" json:"locationCode"`
	DescriptionTh  *string                `column:"description_th" json:"descriptionTh"`
	DescriptionEn  *string                `column:"description_en" json:"descriptionEn"`
	BranchCode     *string                `column:"branch_code" json:"branchCode"`
	IsSaleLocation bool                   `column:"is_sale_location" json:"isSaleLocation"`
	CountryCode    *string                `column:"country_code" json:"countryCode"`
	RegionCode     *string                `column:"region_code" json:"regionCode"`
	CityCode       *string                `column:"city_code" json:"cityCode"`
	PostCode       *string                `column:"post_code" json:"postCode"`
	IsActive       bool                   `column:"is_active" json:"isActive"`
	CompanyCode    *string                `column:"company_code" json:"companyCode"`
	IsDeletedByErp bool                   `column:"is_deleted_by_erp" json:"isDeletedByErp"`
	LatestSyncDate *time.Time             `column:"latest_sync_date" json:"latestSyncDate"`
	CreatedUser    *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:UserID"`
	UpdatedUser    *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:UserID"`
}

func (MasterAssetLocation) TableName() string {
	return "master_asset_location"
}
