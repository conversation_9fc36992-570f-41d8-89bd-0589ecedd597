package service

import (
	"content-service/internal/model/dto"
	buyerRepository "content-service/internal/repository/buyer"
	buyerAddressRepository "content-service/internal/repository/buyer_address"
	buyerEditProfileRequestRepository "content-service/internal/repository/buyer_edit_profile_request"
	buyerEditProfileRequestCurrentRepository "content-service/internal/repository/buyer_edit_profile_request_current"
	buyerEditProfileRequestPastRepository "content-service/internal/repository/buyer_edit_profile_request_past"
)

type buyerEditprofileRequestService struct {
	BuyerRepo                          buyerRepository.BuyerRepository
	BuyerEditProfileRequestRepo        buyerEditProfileRequestRepository.BuyerEditProfileRequestRepository
	BuyerEditProfileRequestCurrentRepo buyerEditProfileRequestCurrentRepository.BuyerEditProfileRequestCurrentRepository
	BuyerEditProfileRequestPastRepo    buyerEditProfileRequestPastRepository.BuyerEditProfileRequestPastRepository
	BuyerAddressRepo                   buyerAddressRepository.BuyerAddressRepository
}

type BuyerEditprofileRequestService interface {
	FindBuyerEditProfileWithFilter(req dto.BuyerEditProfileSearchReqDto) (dto.BuyerEditProfilePageRespDto[dto.BuyerEditProfileSearchRespDto], error)
	FindBuyerEditProfileWithFilterMasking(req dto.BuyerEditProfileSearchReqDto) (dto.BuyerEditProfilePageRespDto[dto.BuyerEditProfileSearchRespDto], error)
	UpdateBuyerEditProfileRequestStatus(req dto.UpdateBuyerEditProfileStatusReqDto) error
	GetBuyerEditProfileById(id int) (dto.BuyerEditProfileViewDto, error)
	GetBuyerEditProfileByIdMasking(id int) (dto.BuyerEditProfileViewDto, error)
	GetBuyerEditProfileLogById(id int) (dto.BuyerEditProfileViewDto, error)
	GetBuyerEditProfileLogByIdMasking(id int) (dto.BuyerEditProfileViewDto, error)
}

func NewBuyerEditprofileRequestService(buyer buyerRepository.BuyerRepository,
	buyerEditProfileRequestRepo buyerEditProfileRequestRepository.BuyerEditProfileRequestRepository,
	buyerEditProfileRequestCurrentRepo buyerEditProfileRequestCurrentRepository.BuyerEditProfileRequestCurrentRepository,
	buyerEditProfileRequestPastRepo buyerEditProfileRequestPastRepository.BuyerEditProfileRequestPastRepository,
	buyerAddressRepo buyerAddressRepository.BuyerAddressRepository,
) BuyerEditprofileRequestService {
	return &buyerEditprofileRequestService{BuyerRepo: buyer,
		BuyerEditProfileRequestRepo:        buyerEditProfileRequestRepo,
		BuyerEditProfileRequestCurrentRepo: buyerEditProfileRequestCurrentRepo,
		BuyerEditProfileRequestPastRepo:    buyerEditProfileRequestPastRepo,
		BuyerAddressRepo:                   buyerAddressRepo}
}
