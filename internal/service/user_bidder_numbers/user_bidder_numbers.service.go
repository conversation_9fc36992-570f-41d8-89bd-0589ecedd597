package service

import (
	"content-service/internal/model/dto"
	auctionRepository "content-service/internal/repository/auction"
	lotRepository "content-service/internal/repository/lot"
	lotSouLotLineRepository "content-service/internal/repository/lot_sou_lot_line"
	repository "content-service/internal/repository/user_bidder_numbers"
)

type userBidderNumbersService struct {
	Repo              repository.UserBidderNumbersRepository
	LotRepo           lotRepository.LotRepository
	LotSouLotLineRepo lotSouLotLineRepository.LotSouLotLineRepository
	AuctionRepo       auctionRepository.AuctionRepository
}

type UserBidderNumbersService interface {
	GetUserBidderNumberByBidderNumber(bidderNumberId string, lotSouLotLineId int) (dto.UserBidderNumbersRespDto, error)
}

func NewUserBidderNumbersService(repo repository.UserBidderNumbersRepository, lotRepo lotRepository.LotRepository, lotSouLotLineRepo lotSouLotLineRepository.LotSouLotLineRepository, auctionRepo auctionRepository.AuctionRepository) UserBidderNumbersService {
	return &userBidderNumbersService{Repo: repo, LotRepo: lotRepo, LotSouLotLineRepo: lotSouLotLineRepo, AuctionRepo: auctionRepo}
}
