package service

import (
	constant_content "content-service/constant"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"fmt"
	"net/http"

	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/util"

	"gorm.io/gorm"
)

func (s *userBidderNumbersService) GetUserBidderNumberByBidderNumber(bidderNumberId string, lotSouLotLineId int) (dto.UserBidderNumbersRespDto, error) {
	resp := dto.UserBidderNumbersRespDto{}
	//NOTE - find lot_sou_lot_line by id
	lotSouLotLine, err := s.LotSouLotLineRepo.GetById(lotSouLotLineId)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}
	//NOTE - find lot
	lot, err := s.LotRepo.GetById(lotSouLotLine.LotId)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}
	//NOTE - find auction
	auction, err := s.AuctionRepo.GetById(lot.AuctionId)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	result := &entity.UserBidderNumbers{}
	switch *auction.AuctionTypeId {
	case constant_content.AUCTION_TYPE_SPECIAL:
		result, err = s.Repo.FindUserBidderNumberByBidderNumberAndLotId(bidderNumberId, lotSouLotLine.LotId, util.Val(lotSouLotLine.SoldDate))
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return resp, errs.NewBusinessError(http.StatusBadRequest, constant.Invalidate, "bidder number not found", "error.userBidderNumbers.notFound")
			}
			return resp, errs.NewError(http.StatusInternalServerError, err)
		}
	case constant_content.AUCTION_TYPE_NORMAL:
		result, err = s.Repo.FindUserBidderNumberByBidderNumber(bidderNumberId, util.Val(lotSouLotLine.SoldDate))
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return resp, errs.NewBusinessError(http.StatusBadRequest, constant.Invalidate, "bidder number not found", "error.userBidderNumbers.notFound")
			}
			return resp, errs.NewError(http.StatusInternalServerError, err)
		}
	}

	//NOTE - Map to DTO
	resp = util.MapToWithCreatedByAndUpdatedBy[dto.UserBidderNumbersRespDto](result)
	resp.UserId = result.UserId
	resp.BidderNumber = result.BidderNumber
	resp.BuyerName = util.Ptr(fmt.Sprintf("%s %s %s %s", util.Val(result.PrefixName), util.Val(result.BuyerFirstName), util.Val(result.BuyerMiddleName), util.Val(result.BuyerLastName)))

	return resp, nil
}
