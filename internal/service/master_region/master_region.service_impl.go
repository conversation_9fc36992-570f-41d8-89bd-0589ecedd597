package service

import (
	"backend-common-lib/constant"
	"backend-common-lib/erp"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_region"
	"fmt"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (s *masterRegionService) SearchMasterRegionFilter(req dto.MasterRegionPageReqDto) (dto.MasterRegionPageRespDto[dto.MasterRegionDto], error) {
	resp := dto.MasterRegionPageRespDto[dto.MasterRegionDto]{}
	result, err := s.Repo.FindMasterRegionWithFilter(req)
	if err != nil {
		return resp, errs.NewError(fiber.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountMasterRegionWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.MasterRegionDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.MasterRegionDto](v)
	}

	//NOTE Get latest sync date
	var latestSyncDate *time.Time
	if len(result) > 0 {
		latestSyncDate = result[0].LatestSyncDate
	} else {
		latestSyncDate, err = s.Repo.FindLatestSyncDate()
		if err != nil {
			return resp, errs.NewError(fiber.StatusInternalServerError, err)
		}
	}

	//NOTE - Response
	resp = dto.MasterRegionPageRespDto[dto.MasterRegionDto]{
		PagingModel:    *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
		LatestSyncDate: latestSyncDate,
	}

	return resp, nil
}

func (s *masterRegionService) UpdateMasterRegionStatus(req dto.MasterRegionUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdatesMasterRegionFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}

func (s *masterRegionService) SyncMasterRegionFromErp(actionBy *int, erpConfig global.ErpConfig) error {
	erpMap, allKeys, err := s.getRegionFromErp(erpConfig)
	if err != nil {
		return err
	}
	dbMap, err := s.getRegionFromDb(allKeys)
	if err != nil {
		return err
	}
	err = s.syncRegion(actionBy, erpMap, dbMap, allKeys)
	if err != nil {
		return err
	}

	return nil
}

func (s *masterRegionService) getRegionFromErp(erpConfig global.ErpConfig) (map[string]dto.MasterRegionDto, map[string]struct{}, error) {
	erpList, err := erp.FetchListFromErp[dto.MasterRegionSyncErpRespDto](
		erpConfig.RegionUrl,
		erpConfig.LoginUrl,
		erpConfig.Email,
		erpConfig.Password,
		map[string]interface{}{"Company_Name": "AUCT"},
		"data",
	)
	if err != nil {
		return nil, nil, errs.NewError(http.StatusInternalServerError, err)
	}

	erpMap := make(map[string]dto.MasterRegionDto)
	allKeys := make(map[string]struct{})

	for _, e := range erpList {
		mapKey := util.Val(e.CountryCode) + "|" + util.Val(e.RegionCode)
		erpMap[mapKey] = dto.MasterRegionDto{
			CompanyCode:   e.CompanyCode,
			RegionCode:    e.RegionCode,
			CountryCode:   e.CountryCode,
			DescriptionTh: e.DescriptionTh,
			DescriptionEn: e.DescriptionEn,
			IsActive:      e.Status == "Active",
		}
		allKeys[mapKey] = struct{}{}
	}
	return erpMap, allKeys, nil
}

func (s *masterRegionService) getRegionFromDb(allKeys map[string]struct{}) (map[string]entity.MasterRegion, error) {
	dbList, err := s.Repo.FindMasterRegionAll()
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	dbMap := make(map[string]entity.MasterRegion)
	for _, e := range dbList {
		mapKey := util.Val(e.CountryCode) + "|" + util.Val(e.RegionCode)
		dbMap[mapKey] = e
		allKeys[mapKey] = struct{}{}
	}
	return dbMap, nil
}

func (s *masterRegionService) syncRegion(actionBy *int, erpMap map[string]dto.MasterRegionDto, dbMap map[string]entity.MasterRegion, allKeys map[string]struct{}) error {
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		repo := repository.NewMasterRegionRepository(tx)
		var toInsert []entity.MasterRegion
		currentDateTime := util.NowPtr()

		for code := range allKeys {
			erp, erpExists := erpMap[code]
			db, dbExists := dbMap[code]

			if erpExists && dbExists {
				// มีทั้ง ERP + DB → compare → update หมด
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.CompanyCode = erp.CompanyCode
				temp.RegionCode = erp.RegionCode
				temp.CountryCode = erp.CountryCode
				temp.DescriptionTh = erp.DescriptionTh
				temp.DescriptionEn = erp.DescriptionEn
				temp.IsActive = erp.IsActive
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterRegionAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}

			} else if erpExists && !dbExists {
				// มีใน ERP แต่ไม่มีใน DB → insert
				temp := entity.MasterRegion{
					BaseEntity: &model.BaseEntity{
						CreatedDate: *currentDateTime,
						CreatedBy:   actionBy,
						UpdatedDate: currentDateTime,
						UpdatedBy:   actionBy,
					},
					CompanyCode:    erp.CompanyCode,
					RegionCode:     erp.RegionCode,
					CountryCode:    erp.CountryCode,
					DescriptionTh:  erp.DescriptionTh,
					DescriptionEn:  erp.DescriptionEn,
					IsActive:       erp.IsActive,
					IsDeletedByErp: false,
					LatestSyncDate: currentDateTime,
				}
				toInsert = append(toInsert, temp)

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterRegionAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}
		}

		if len(toInsert) > 0 {
			if err := repo.InsertMasterRegionList(toInsert); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}
		return nil
	})
}
