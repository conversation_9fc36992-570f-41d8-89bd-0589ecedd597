#!/bin/bash


if [ -n "$1" ]; then
  env=$1
else
  echo "Select environment to run:"
  echo "1) local"
  echo "2) dev"
  echo "3) sit"
  echo "4) uat"
  echo "5) prod"
  read -p "Enter environment number or name: " env
fi

case "$env" in
  ""|1|local) export APP_ENV=local ;;
  2|dev) export APP_ENV=dev ;;
  3|sit) export APP_ENV=sit ;;
  4|uat) export APP_ENV=uat ;;
  5|prod) export APP_ENV=prod ;;
  *) echo "Invalid environment selection"; exit 1 ;;
esac

echo "Running content_service..."
nodemon --watch . --ext go --exec "go run cmd/main.go" --signal SIGTERM