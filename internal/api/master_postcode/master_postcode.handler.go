package masterpostcode

import (
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	"content-service/internal/model/dto"
	service "content-service/internal/service/master_postcode"
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"
)

type Handler struct {
	Service   service.MasterPostcodeService
	ErpConfig global.ErpConfig
}

func (h *Handler) SearchMasterPostcodeFilter(c *fiber.Ctx) error {
	var req dto.MasterPostcodePageReqDto

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	res, err := h.Service.SearchMasterPostcodeFilter(req)
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) UpdateMasterPostcodeStatus(c *fiber.Ctx) error {
	var req dto.MasterPostcodeUpdateReqDto

	if err := c.<PERSON>(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	idStr := c.Params("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}
	req.Id = id

	actionBy := util.GetActionByFromHeader(c)
	req.ActionBy = actionBy

	err = h.Service.UpdateMasterPostcodeStatus(req)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}

func (h *Handler) SyncMasterPostcodeFromErp(c *fiber.Ctx) error {
	var req model.BaseDtoActionBy

	actionBy := util.GetActionByFromHeader(c)
	req.ActionBy = actionBy

	err := h.Service.SyncMasterPostcodeFromErp(req.ActionBy, h.ErpConfig)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}

func (h *Handler) GetPostCodeBySubDistrictID(c *fiber.Ctx) error {
	subDistrictID := c.Query("subDistrictId")

	var parsedSubDistrictID int

	if subDistrictID == "" {
		return errs.NewError(http.StatusBadRequest, nil)
	} else {
		parsed, convErr := strconv.Atoi(subDistrictID)
		if convErr != nil {
			return errs.NewError(http.StatusBadRequest, nil)
		}

		parsedSubDistrictID = parsed
	}

	res, err := h.Service.GetPostCodeBySubDistrictID(parsedSubDistrictID)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}
