package member

import (
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	service "content-service/internal/service/member"
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"
)

type Handler struct {
	Service service.MemberService
}

func (h *Handler) SearchMemberWithFilter(c *fiber.Ctx) error {
	var req dto.MemberSearchReqDto
	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}
	data, err := h.Service.SearchMemberWithFilter(req)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&data))
}

func (h *Handler) UpdateMemberStatus(c *fiber.Ctx) error {
	var req dto.UpdateMemberStatusReqDto

	if err := c.Body<PERSON>er(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	idStr := c.Params("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}
	req.Id = id

	actionBy := util.GetActionByFromHeader(c)
	req.ActionBy = actionBy

	err = h.Service.UpdateMemberStatus(req)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}

func (h *Handler) GetBuyerRegistrationRequestFlowChart(c *fiber.Ctx) error {
	data, err := h.Service.GetBuyerRegistrationRequestFlowChart()
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&data))
}
