package repository

import (
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type passwordExpiryRepositoryImpl struct {
	DB *gorm.DB
}

type PasswordExpiryRepository interface {
	FindAllPasswordExpiry() ([]entity.PasswordExpiry, error)
	UpdatesPasswordExpiryFieldsWhere(fields map[string]interface{}, id int) (int64, *entity.PasswordExpiry, error)

	GetDB() *gorm.DB
}

func NewPasswordExpiryRepository(db *gorm.DB) PasswordExpiryRepository {
	return &passwordExpiryRepositoryImpl{DB: db}
}
