package repository

import (
	"backend-common-lib/util"
	"content-service/constant"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"fmt"
	"strings"

	"gorm.io/gorm"
)

func (r *buyerRepositoryImpl) GetById(id int) (entity.Buyer, error) {
	var buyer entity.Buyer
	err := r.DB.First(&buyer, id).Error
	return buyer, err
}

func (r *buyerRepositoryImpl) GetByIdWithPrefix(id int) (entity.Buyer, error) {
	var buyer entity.Buyer
	err := r.DB.Table("buyer").Select(
		`buyer.*,
		u.username,
		pa.description_th AS prefix_name_th,
		pa.description_en AS prefix_name_en,
		nationality.nationality_th AS nationality_th,
		nationality.nationality_en AS nationality_en`).
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_prefix_name) AS pa ON pa.id = buyer.prefix_name_id").
		Joins("LEFT JOIN (SELECT id,username FROM users) AS u ON u.id = buyer.user_id").
		Joins("LEFT JOIN (SELECT id,nationality_th,nationality_en FROM master_country) AS nationality ON nationality.id = buyer.nationality_id").
		First(&buyer, id).Error
	return buyer, err
}

func (r *buyerRepositoryImpl) GetAllByIds(ids []int) ([]*entity.Buyer, error) {
	var buyers []*entity.Buyer
	err := r.DB.Table("buyer").Select(
		`buyer.*,
		u.username,
		pa.description_th AS prefix_name_th,
		pa.description_en AS prefix_name_en,
		nationality.nationality_th AS nationality_th,
		nationality.nationality_en AS nationality_en`).
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_prefix_name) AS pa ON pa.id = buyer.prefix_name_id").
		Joins("LEFT JOIN (SELECT id,username FROM users) AS u ON u.id = buyer.user_id").
		Joins("LEFT JOIN (SELECT id,nationality_th,nationality_en FROM master_country) AS nationality ON nationality.id = buyer.nationality_id").
		Where("buyer.id IN ?", ids).Find(&buyers).Error
	return buyers, err
}

func (r *buyerRepositoryImpl) FindBuyerWithFilter(req dto.MemberSearchReqDto) ([]entity.Buyer, error) {
	var results []entity.Buyer

	query := r.DB.Model(&entity.Buyer{}).
		Select(`buyer.*,
		CONCAT_WS(' ',first_name,NULLIF(middle_name, ''),last_name) AS full_name,
		master_prefix_name.description_th AS prefix_name_th,
		master_prefix_name.description_en AS prefix_name_en,
		user_bidder_numbers.bidder_number AS bidder_number,
		u.username,
		u.is_active AS user_is_active,
		mcg.description_th AS customer_group_desc,
		CASE
            WHEN buyer.is_blacklist THEN 'BLACKLIST'
            WHEN buyer.is_block THEN 'BLOCKED'
            WHEN brr.approval_status IS NOT NULL THEN brr.approval_status::TEXT
        END AS account_status_code,
		CASE 
			WHEN (
				buyer.can_view_auction = true
				AND buyer.can_bid = true
				AND buyer.can_make_payment = true
				AND buyer.is_approved = true
				AND buyer.is_deposit_paid = true 
			) OR buyer.is_block = true
			THEN 'ACTIVE' 
			ELSE 'INACTIVE'
		END AS user_status_code`,
		).
		Joins("LEFT JOIN master_prefix_name ON master_prefix_name.id = buyer.prefix_name_id").
		Joins("LEFT JOIN users u ON buyer.user_id = u.id").
		Joins("LEFT JOIN user_bidder_numbers ON user_bidder_numbers.user_id = buyer.user_id AND user_bidder_numbers.lot_id IS NULL AND user_bidder_numbers.auction_date = current_date"). // left join user_bidder_numbers and lot_id = null and auction date today
		Joins("LEFT JOIN master_customer_group mcg ON buyer.customer_group_id = mcg.id").
		Joins(`
				LEFT JOIN LATERAL (
					SELECT *
					FROM buyer_registration_request brr
					WHERE brr.buyer_id = buyer.id
					ORDER BY brr.created_date DESC
					LIMIT 1
				) brr ON true
			`)

	query = query.Where("u.manager_id is null")

	if req.CustomerGroupId != nil {
		query = query.Where("customer_group_id = ?", req.CustomerGroupId)
	}

	if req.Username != nil {
		query = query.Where("username LIKE ?", fmt.Sprintf("%%%s%%", *req.Username))
	}

	if req.BidderId != nil {
		query = query.Where("bidder_id LIKE ?", fmt.Sprintf("%%%s%%", *req.BidderId))
	}

	if req.FullName != nil {
		query = query.Where("CONCAT_WS(' ',first_name,NULLIF(middle_name, ''),last_name) LIKE ?", fmt.Sprintf("%%%s%%", *req.FullName))
	}

	if req.IdentificationNumber != nil {
		query = query.Where("identification_number LIKE ?", fmt.Sprintf("%%%s%%", *req.IdentificationNumber))
	}

	if req.SortBy != "" {
		if req.SortBy == "isActive" {
			orderClause := fmt.Sprintf(`
				CASE
					WHEN buyer.is_blacklist = true THEN 1
					WHEN buyer.is_block = true THEN 2
					ELSE 3
				END %s`, req.SortOrder)
			query = query.Order(orderClause)
		} else {
			snakeSort := util.CamelToSnake(req.SortBy)
			query = util.ApplySortFromMapField(query, snakeSort, req.SortOrder, "buyer", constant.SortingMember, false)
		}
	}
	query.Order("buyer.created_date desc, buyer.updated_date desc")

	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)
	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *buyerRepositoryImpl) CountBuyerWithFilter(req dto.MemberSearchReqDto) (int64, error) {
	var count int64

	query := r.DB.Model(&entity.Buyer{}).Select("buyer.*,CONCAT(first_name, middle_name, last_name) AS name,master_prefix_name.description_th AS prefix,user_bidder_numbers.bidder_number AS bidder_number").
		Joins("LEFT JOIN master_prefix_name ON master_prefix_name.id = buyer.prefix_name_id").
		Joins("LEFT JOIN users u ON buyer.user_id = u.id").
		Joins("LEFT JOIN user_bidder_numbers ON user_bidder_numbers.user_id = buyer.user_id AND user_bidder_numbers.lot_id IS NULL AND user_bidder_numbers.auction_date = current_date").
		Preload("CustomerGroupForJoin")

	query = query.Where("u.manager_id is null")

	if req.CustomerGroupId != nil {
		query = query.Where("customer_group_id = ?", req.CustomerGroupId)
	}

	if req.Username != nil {
		query = query.Where("username LIKE ?", fmt.Sprintf("%%%s%%", *req.Username))
	}

	if req.BidderId != nil {
		query = query.Where("bidder_id LIKE ?", fmt.Sprintf("%%%s%%", *req.BidderId))
	}

	if req.FullName != nil {
		query = query.Where("CONCAT(first_name, middle_name, last_name) LIKE ?", fmt.Sprintf("%%%s%%", *req.FullName))
	}

	if req.IdentificationNumber != nil {
		query = query.Where("identification_number LIKE ?", fmt.Sprintf("%%%s%%", *req.IdentificationNumber))
	}

	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}

	return count, nil
}

func (r *buyerRepositoryImpl) UpdateAllFields(buyer entity.Buyer) error {
	if err := r.DB.Save(buyer).Error; err != nil {
		return err
	}
	return nil
}

func (r *buyerRepositoryImpl) UpdateStatus(buyerId int, fields map[string]interface{}) error {
	err := r.DB.Model(&entity.Buyer{}).Where("id = ?", buyerId).Updates(fields).Error
	return err
}
func (r *buyerRepositoryImpl) UpdateStatusTx(tx *gorm.DB, buyerId int, fields map[string]interface{}) error {
	err := tx.Model(&entity.Buyer{}).Where("id = ?", buyerId).Updates(fields).Error
	return err
}

func (r *buyerRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}

func (r *buyerRepositoryImpl) FindBuyerDetailsWithFilter(req dto.BuyerSearchReqDto) (*entity.Buyer, error) {
	var results *entity.Buyer

	query := r.DB.Model(&entity.Buyer{}).Select(
		`
			buyer.*,
			CONCAT_WS(' ', buyer.first_name, buyer.middle_name, buyer.last_name) AS name,
			mpn.id AS prefix,
			mpn.description_th AS prefix_name_th,
			mpn.description_en AS prefix_name_en
	`).
		Joins("LEFT JOIN master_prefix_name mpn ON mpn.id = buyer.prefix_name_id").
		Preload("CustomerGroupForJoin")

	if req.Keyword != nil {
		kw := strings.TrimSpace(util.Val(req.Keyword))
		kw = strings.Join(strings.Fields(kw), " ")

		query = query.Where(
			`
				buyer.bidder_id = ? OR 
				buyer.identification_number = ? OR 
				buyer.customer_no = ? OR 
				CONCAT_WS(' ', buyer.first_name, buyer.middle_name, buyer.last_name) = ?
			`,
			kw,
			kw,
			kw,
			kw,
		)
	}

	if err := query.First(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *buyerRepositoryImpl) GetLatestBuyerLogByBuyerId(id int) (entity.BuyerLog, error) {
	var buyerLog entity.BuyerLog
	err := r.DB.Table("buyer_log").
		Select("id").
		Where("buyer_id = ?", id).
		Order("id DESC").
		Limit(1).First(&buyerLog).Error
	return buyerLog, err
}
