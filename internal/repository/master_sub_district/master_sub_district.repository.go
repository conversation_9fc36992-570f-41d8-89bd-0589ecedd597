package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type masterSubDistrictRepositoryImpl struct {
	DB *gorm.DB
}

type MasterSubDistrictRepository interface {
	FindMasterSubDistrictWithFilter(req dto.MasterSubDistrictPageReqDto) ([]entity.MasterSubDistrict, error)
	CountMasterSubDistrictWithFilter(req dto.MasterSubDistrictPageReqDto) (int64, error)
	FindMasterSubDistrictLatestSyncDate() (*time.Time, error)
	UpdatesMasterSubDistrictFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)
	FindMasterSubDistrictAll() ([]entity.MasterSubDistrict, error)
	UpdateMasterSubDistrictAllFields(e *entity.MasterSubDistrict) error
	InsertMasterSubDistrictListWithBatches(data []entity.MasterSubDistrict) error
	GetMasterSubDistrictByPostCodeAndDistrictID(postCode *string, districtID int) ([]entity.MasterSubDistrict, error)

	GetDB() *gorm.DB
}

func NewMasterSubDistrictRepository(db *gorm.DB) MasterSubDistrictRepository {
	return &masterSubDistrictRepositoryImpl{DB: db}
}
