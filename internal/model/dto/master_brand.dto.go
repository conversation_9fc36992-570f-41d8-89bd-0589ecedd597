package dto

import (
	"backend-common-lib/model"
	"time"
)

type MasterBrandDto struct {
	model.BaseDto
	BrandCode      *string `json:"brandCode"`
	Description    *string `json:"description"`
	AssetTypeCode  *string `json:"assetTypeCode"`
	IsActive       bool    `json:"isActive"`
	CompanyCode    *string `json:"companyCode"`
	IsDeletedByErp bool    `json:"isDeletedByErp"`
}

type MasterBrandPageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate" example:"2020-01-01"`
}

type MasterBrandSyncErpRespDto struct {
	BrandCode     *string `json:"Brand_Code"`
	Description   *string `json:"Description"`
	AssetTypeCode *string `json:"Asset_Type_Code"`
	CompanyCode   *string `json:"CompanyCode"`
	Status        string  `json:"Status"`
}

type MasterBrandListDto struct {
	MasterBrandList []MasterBrandDto `json:"brandMasterList"`
}
