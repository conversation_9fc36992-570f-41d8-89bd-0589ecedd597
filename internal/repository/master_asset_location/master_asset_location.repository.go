package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type masterAssetLocationRepositoryImpl struct {
	DB *gorm.DB
}

type MasterAssetLocationRepository interface {
	FindMasterAssetLocationWithFilter(req dto.MasterAssetLocationPageReqDto) ([]entity.MasterAssetLocation, error)
	CountMasterAssetLocationWithFilter(req dto.MasterAssetLocationPageReqDto) (int64, error)
	FindMasterAssetLocationLatestSyncDate() (*time.Time, error)
	UpdatesMasterAssetLocationFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)
	FindMasterAssetLocationAll() ([]entity.MasterAssetLocation, error)
	UpdateMasterAssetLocationAllFields(e *entity.MasterAssetLocation) error
	InsertMasterAssetLocationList(data []entity.MasterAssetLocation) error
	FindById(id int) (*entity.MasterAssetLocation, error)

	GetDB() *gorm.DB
}

func NewMasterAssetLocationRepository(db *gorm.DB) MasterAssetLocationRepository {
	return &masterAssetLocationRepositoryImpl{DB: db}
}
