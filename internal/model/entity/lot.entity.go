package entity

import (
	"backend-common-lib/model"
	"time"

	"gorm.io/gorm"
)

type Lot struct {
	*model.BaseEntity
	BranchId             int             `gorm:"column:branch_id" json:"branchId"`
	AuctionDate          time.Time       `gorm:"column:auction_date" json:"auctionDate"`
	AuctionTime          time.Time       `gorm:"column:auction_time" json:"auctionTime"`
	FloorId              int             `gorm:"column:floor_id" json:"floorId"`
	AuctionId            int             `gorm:"column:auction_id" json:"auctionId"`
	Name                 string          `gorm:"column:name;size:255" json:"name"`
	Description          string          `gorm:"column:description;size:255" json:"description"`
	IsActive             *bool           `gorm:"column:is_active" json:"isActive"`
	FloorStatus          int             `gorm:"column:floor_status" json:"floorStatus"`
	AuctionChannel       int             `gorm:"column:auction_channel;size:100" json:"auctionChannel"`
	ShowStartDate        time.Time       `gorm:"column:show_start_date" json:"showStartDate"`
	ShowStartTime        *time.Time      `gorm:"column:show_start_time" json:"showStartTime"`
	ShowEndDate          time.Time       `gorm:"column:show_end_date" json:"showEndDate"`
	ShowEndTime          *time.Time      `gorm:"column:show_end_time" json:"showEndTime"`
	ProxyStartDate       *time.Time      `gorm:"column:proxy_start_date" json:"proxyStartDate"`
	ProxyStartTime       *time.Time      `gorm:"column:proxy_start_time" json:"proxyStartTime"`
	VideoStatus          int             `gorm:"column:video_status" json:"videoStatus"`
	SoundStatus          int             `gorm:"column:sound_status" json:"soundStatus"`
	AuctioneerUserId     int             `gorm:"column:auctioneer_user_id" json:"auctioneerUserId"`
	DeletedDate          *gorm.DeletedAt `gorm:"column:deleted_date" json:"deletedDate"`
	TotalOnlineJoinedBid *int            `gorm:"column:total_online_joined_bid" json:"totalOnlineJoinedBid"`
	// LotActiveOptions     []LotActiveOption                `gorm:"-"`
	// LotAssetTypes        []LotAssetType                   `gorm:"-"`
	// LotStreamings        []LotStreaming                   `gorm:"-"`
	// LotSous              []LotSou                         `gorm:"-"`
	Branch     *model.BranchForJoin             `gorm:"foreignKey:BranchId;references:ID;->"`
	Floor      *model.AssetLocationFloorForJoin `gorm:"foreignKey:FloorId;references:ID;->"`
	Auctioneer *model.EmployeeForJoin           `gorm:"foreignKey:AuctioneerUserId;references:UserID;->"`
}

func (Lot) TableName() string {
	return "lot"
}
