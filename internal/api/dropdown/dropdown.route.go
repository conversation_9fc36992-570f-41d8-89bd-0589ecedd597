package dropdown

import (
	"content-service/internal/global"
	auctionAssetRepo "content-service/internal/repository/auction_asset"
	auctionNameRepo "content-service/internal/repository/auction_name"
	buyerRepo "content-service/internal/repository/buyer"
	config_parameter "content-service/internal/repository/config_parameter"
	helpRequestReasonRepo "content-service/internal/repository/help_request_reason"
	lotRepo "content-service/internal/repository/lot"
	assetGroupRepo "content-service/internal/repository/master_asset_group"
	assetLocationRepo "content-service/internal/repository/master_asset_location"
	yardRepo "content-service/internal/repository/master_asset_location_floor"
	assetTypeRepo "content-service/internal/repository/master_asset_type"
	auctionTypeRepo "content-service/internal/repository/master_auction_type"
	bankRepo "content-service/internal/repository/master_bank"
	branchRepo "content-service/internal/repository/master_branch"
	brandRepo "content-service/internal/repository/master_brand"
	cityRepo "content-service/internal/repository/master_city"
	countryRepo "content-service/internal/repository/master_country"
	customerGroupRepo "content-service/internal/repository/master_customer_group"
	customerTypeRepo "content-service/internal/repository/master_customer_type"
	departmentRepo "content-service/internal/repository/master_department"
	districtRepo "content-service/internal/repository/master_district"
	eventRepo "content-service/internal/repository/master_event"
	modelRepo "content-service/internal/repository/master_model"
	paymentMethodRepo "content-service/internal/repository/master_payment_method"
	prefixNameRepo "content-service/internal/repository/master_prefix_name"
	reasonRepo "content-service/internal/repository/master_reason"
	regionRepo "content-service/internal/repository/master_region"
	registerTypeRepo "content-service/internal/repository/master_register_type"
	registerTypeCarRepo "content-service/internal/repository/master_register_type_car"
	saleChannelRepo "content-service/internal/repository/master_sale_channel"
	subDistrictRepo "content-service/internal/repository/master_sub_district"
	vatBusinessRepo "content-service/internal/repository/master_vat_business"
	vatCodeRepo "content-service/internal/repository/master_vat_code"
	vendorRepo "content-service/internal/repository/master_vendor"
	vendorGroupRepo "content-service/internal/repository/master_vendor_group"
	proxyBidCancelReasonRepo "content-service/internal/repository/proxy_bid_cancel_reason"
	reprintSlipReasonRepo "content-service/internal/repository/reprint_slip_reason"
	dropdownService "content-service/internal/service/dropdown"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	assetTypeRepository := assetTypeRepo.NewMasterAssetTypeRepository(db)
	assetGroupRepository := assetGroupRepo.NewMasterAssetGroupRepository(db)
	eventRepository := eventRepo.NewMasterEventRepository(db)
	vendorRepository := vendorRepo.NewMasterVendorRepository(db)
	vendorGroupRepository := vendorGroupRepo.NewMasterVendorGroupRepository(db)
	saleChannelRepository := saleChannelRepo.NewMasterSaleChannelRepository(db)
	branchRepository := branchRepo.NewMasterBranchRepository(db)
	lotSettingRepository := config_parameter.NewConfigParameterRepository(db)
	floorStatusRepository := config_parameter.NewConfigParameterRepository(db)
	configFeatureLotRepository := config_parameter.NewConfigParameterRepository(db)
	saleChannelConfigRepository := config_parameter.NewConfigParameterRepository(db)
	auctionStatusRepository := config_parameter.NewConfigParameterRepository(db)
	prefixNameRepository := prefixNameRepo.NewMasterPrefixNameRepository(db)
	vatBusinessRepository := vatBusinessRepo.NewMasterVatBusinessRepository(db)
	dayBeforeDueRepository := config_parameter.NewConfigParameterRepository(db)
	countryRepository := countryRepo.NewMasterCountryRepository(db)
	regionRepository := regionRepo.NewMasterRegionRepository(db)
	yardRepository := yardRepo.NewMasterAssetLocationFloorRepository(db)
	vatCodeRepository := vatCodeRepo.NewMasterVatCodeRepository(db)
	districtRepository := districtRepo.NewMasterDistrictRepository(db)
	cityRepository := cityRepo.NewMasterCityRepository(db)
	nationalityRepository := config_parameter.NewConfigParameterRepository(db)
	subDistrictRepository := subDistrictRepo.NewMasterSubDistrictRepository(db)
	customerTypeRepository := customerTypeRepo.NewMasterCustomerTypeRepository(db)
	customerGroupRepository := customerGroupRepo.NewMasterCustomerGroupRepository(db)
	departmentRepository := departmentRepo.NewMasterDepartmentRepository(db)
	auctionTypeRepository := auctionTypeRepo.NewMasterAuctionTypeRepository(db)
	auctionNameRepository := auctionNameRepo.NewAuctionNameRepository(db)
	buyerRepository := buyerRepo.NewBuyerRepository(db)
	configParameterRepository := config_parameter.NewConfigParameterRepository(db)
	proxyBidCancelReasonRepository := proxyBidCancelReasonRepo.NewProxyBidCancelReasonRepository(db)
	auctionAssetRepository := auctionAssetRepo.NewAuctionAssetRepository(db)
	helpRequestReasonRepository := helpRequestReasonRepo.NewHelpRequestReasonRepository(db)
	reprintSlipReasonRepository := reprintSlipReasonRepo.NewReprintSlipReasonRepository(db)
	auctionDepositStatusRepository := config_parameter.NewConfigParameterRepository(db)
	paymentTypeRepository := config_parameter.NewConfigParameterRepository(db)
	registerTypeRepository := registerTypeRepo.NewMasterRegisterTypeRepository(db)
	registerTypeCarRepository := registerTypeCarRepo.NewMasterRegisterTypeCarRepository(db)
	paymentMethodRepository := paymentMethodRepo.NewMasterPaymentMethodRepository(db)
	bankRepository := bankRepo.NewMasterBankRepository(db)
	brandRepository := brandRepo.NewMasterBrandRepository(db)
	modelRepository := modelRepo.NewMasterModelRepository(db)
	lotRepository := lotRepo.NewLotRepository(db)
	assetLocationRepository := assetLocationRepo.NewMasterAssetLocationRepository(db)
	reasonRepository := reasonRepo.NewMasterReasonRepository(db)

	service := dropdownService.NewDropdownService(
		assetTypeRepository, assetGroupRepository,
		eventRepository, vendorRepository,
		vendorGroupRepository, saleChannelRepository,
		branchRepository, assetLocationRepository, lotSettingRepository,
		floorStatusRepository, configFeatureLotRepository,
		saleChannelConfigRepository, auctionStatusRepository,
		prefixNameRepository, vatBusinessRepository, dayBeforeDueRepository,
		countryRepository, regionRepository, yardRepository,
		vatCodeRepository, districtRepository,
		cityRepository, nationalityRepository,
		subDistrictRepository, customerTypeRepository,
		customerGroupRepository, departmentRepository,
		auctionTypeRepository, auctionNameRepository,
		buyerRepository, configParameterRepository,
		proxyBidCancelReasonRepository,
		auctionAssetRepository,
		helpRequestReasonRepository,
		reprintSlipReasonRepository,
		auctionDepositStatusRepository,
		paymentTypeRepository,
		registerTypeRepository,
		registerTypeCarRepository,
		paymentMethodRepository,
		bankRepository,
		brandRepository,
		modelRepository,
		lotRepository,
		reasonRepository,
	)
	handler := &Handler{Service: service}

	SetupRoutes(r, handler)
}

func SetupRoutes(route fiber.Router, h *Handler) {

	route.Get("asset-types", func(c *fiber.Ctx) error {
		return h.GetAssetTypeDropdown(c)
	})

	route.Get("asset-groups", func(c *fiber.Ctx) error {
		return h.GetAssetGroupDropdown(c)
	})

	route.Post("asset-groups", func(c *fiber.Ctx) error {
		return h.GetAssetGroupDropdownByMultipleAssetType(c)
	})

	route.Get("regions", func(c *fiber.Ctx) error {
		return h.GetRegionDropdown(c)
	})

	route.Get("countries", func(c *fiber.Ctx) error {
		return h.GetCountryDropdown(c)
	})

	route.Get("events", func(c *fiber.Ctx) error {
		return h.GetEventDropdown(c)
	})

	route.Get("vendors", func(c *fiber.Ctx) error {
		return h.GetVendorDropdown(c)
	})

	route.Get("vendor-groups", func(c *fiber.Ctx) error {
		return h.GetVendorGroupDropdown(c)
	})

	route.Get("sale-channels", func(c *fiber.Ctx) error {
		return h.GetSaleChannelDropdown(c)
	})

	route.Get("branches", func(c *fiber.Ctx) error {
		return h.GetBranchDropdown(c)
	})

	route.Get("asset-locations", func(c *fiber.Ctx) error {
		return h.GetAssetLocationDropdown(c)
	})

	route.Get("lot-settings", func(c *fiber.Ctx) error {
		return h.GetLotSettingDropdown(c)
	})

	route.Get("floor-status", func(c *fiber.Ctx) error {
		return h.GetFloorStatusDropdown(c)
	})

	route.Get("configs-feature-lot", func(c *fiber.Ctx) error {
		return h.GetConfigFeatureLotDropdown(c)
	})

	route.Get("sale-channel-configs", func(c *fiber.Ctx) error {
		return h.GetSaleChannelConfigDropdown(c)
	})

	route.Get("auction-status", func(c *fiber.Ctx) error {
		return h.GetAuctionStatusDropdown(c)
	})

	route.Get("prefix-names", func(c *fiber.Ctx) error {
		return h.GetPrefixNameDropdown(c)
	})

	route.Get("vat-businesses", func(c *fiber.Ctx) error {
		return h.GetVatBusinessDropdown(c)
	})

	route.Get("days-before-due", func(c *fiber.Ctx) error {
		return h.GetDayBeforeDueDropdown(c)
	})

	route.Get("regions", func(c *fiber.Ctx) error {
		return h.GetRegionDropdown(c)
	})

	route.Get("floors", func(c *fiber.Ctx) error {
		return h.GetFloorDropdown(c)
	})

	route.Get("floors/by-branch-and-auction-date", func(c *fiber.Ctx) error {
		return h.GetFloorByBranchAndAuctionDateDropdown(c)
	})

	route.Get("vat-codes", func(c *fiber.Ctx) error {
		return h.GetVatCodeDropdown(c)
	})

	route.Get("districts", func(c *fiber.Ctx) error {
		return h.GetDistrictDropdown(c)
	})

	route.Get("cities", func(c *fiber.Ctx) error {
		return h.GetCityDropdown(c)
	})

	route.Get("nationalities", func(c *fiber.Ctx) error {
		return h.GetNationalityDropdown(c)
	})

	route.Get("sub-districts", func(c *fiber.Ctx) error {
		return h.GetSubDistrictDropdown(c)
	})

	route.Get("customer-types", func(c *fiber.Ctx) error {
		return h.GetCustomerTypeDropdown(c)
	})

	route.Get("customer-groups", func(c *fiber.Ctx) error {
		return h.GetCustomerGroupDropdown(c)
	})

	route.Get("departments", func(c *fiber.Ctx) error {
		return h.GetDepartmentDropdown(c)
	})

	route.Get("auction-names", func(c *fiber.Ctx) error {
		return h.GetAuctionNameDropdown(c)
	})

	route.Get("buyers", func(c *fiber.Ctx) error {
		return h.GetBuyerDropdown(c)
	})

	route.Get("proxy-bid-status", func(c *fiber.Ctx) error {
		return h.GetProxyBidStatusDropdown(c)
	})

	route.Get("action-status", func(c *fiber.Ctx) error {
		return h.GetActionStatusDropdown(c)
	})

	route.Get("proxy-bid-cancel-reasons", func(c *fiber.Ctx) error {
		return h.GetProxyBidCancelReasonDropdown(c)
	})

	route.Get("roles", func(c *fiber.Ctx) error {
		return h.GetRoleDropdown(c)
	})

	route.Get("auction-collaterals", func(c *fiber.Ctx) error {
		return h.GetConfigAuctionCollateralDropdown(c)
	})

	route.Get("display-locations", func(c *fiber.Ctx) error {
		return h.GetConfigDisplayLocationDropdown(c)
	})

	route.Get("campaign-event-types", func(c *fiber.Ctx) error {
		return h.GetConfigCampaignEventTypeDropdown(c)
	})

	route.Get("auction-types", func(c *fiber.Ctx) error {
		return h.GetConfigAuctionTypeDropdown(c)
	})

	route.Get("service-types", func(c *fiber.Ctx) error {
		return h.GetConfigAdditionalServiceTypeDropdown(c)
	})

	route.Get("help-requests-status", func(c *fiber.Ctx) error {
		return h.GetHelpRequestStatusDropdown(c)
	})

	route.Get("help-request-reasons", func(c *fiber.Ctx) error {
		return h.GetHelpRequestReasonDropdown(c)
	})

	route.Get("reprint-slip-reasons", func(c *fiber.Ctx) error {
		return h.GetReprintSlipReasonDropdown(c)
	})

	route.Get("auction-bid-void-reasons", func(c *fiber.Ctx) error {
		return h.GetAuctionBidVoidReasonDropdown(c)
	})

	route.Get("answer-types", func(c *fiber.Ctx) error {
		return h.GetAnswerTypeDropdown(c)
	})

	route.Get("survey-types", func(c *fiber.Ctx) error {
		return h.GetSurveyTypeDropdown(c)
	})

	route.Get("survey-criteria", func(c *fiber.Ctx) error {
		return h.GetSurveyCriteriaDropdown(c)
	})

	route.Get("cities/with-postcode", func(c *fiber.Ctx) error {
		return h.GetMasterCityByPostCode(c)
	})

	route.Get("districts/with-postcode-city", func(c *fiber.Ctx) error {
		return h.GetMasterDistrictByPostCodeAndCityID(c)
	})
	route.Get("sub-districts/with-postcode-district", func(c *fiber.Ctx) error {
		return h.GetMasterSubDistrictByPostCodeAndDistrictID(c)
	})
	route.Get("registration-requests-status", func(c *fiber.Ctx) error {
		return h.GetRegistrationRequestDropdown(c)
	})
	route.Get("credit-limit-requests-status", func(c *fiber.Ctx) error {
		return h.GetCreditLimitRequestDropdown(c)
	})
	route.Get("customer-grades", func(c *fiber.Ctx) error {
		return h.GetCustomerGradeDropdown(c)
	})

	route.Get("edit-profile-requests-status", func(c *fiber.Ctx) error {
		return h.GetEditProfileDropdown(c)
	})

	route.Get("buyer-purchase-status", func(c *fiber.Ctx) error {
		return h.GetBuyerPurchaseStatusDropdown(c)
	})

	route.Get("auction-deposit-status", func(c *fiber.Ctx) error {
		return h.GetAuctionDepositStatusDropdown(c)
	})

	route.Get("payment-type", func(c *fiber.Ctx) error {
		return h.GetPaymentTypeDropdown(c)
	})

	route.Get("register-types", func(c *fiber.Ctx) error {
		return h.GetRegisterTypeDropdown(c)
	})

	route.Get("register-vehicle-types", func(c *fiber.Ctx) error {
		return h.GetRegisterTypeCarDropdown(c)
	})

	route.Get("payment-methods", func(c *fiber.Ctx) error {
		return h.GetMasterPaymentMethodDropdown(c)
	})

	route.Get("banks", func(c *fiber.Ctx) error {
		return h.GetMasterBankDropdown(c)
	})

	//TODO - To delete
	route.Get("blacklists", func(c *fiber.Ctx) error {
		return h.GetVendorDropdown(c)
	})

	//Brand
	route.Get("brands", func(c *fiber.Ctx) error {
		return h.GetMasterBrandDropdown(c)
	})

	//Model
	route.Get("models", func(c *fiber.Ctx) error {
		return h.GetMasterModelDropdown(c)
	})

	//CarTypeCon
	route.Get("carTypeCon", func(c *fiber.Ctx) error {
		return h.GetConfigCarTypeConDropdown(c)
	})

	route.Get("structureGrade", func(c *fiber.Ctx) error {
		return h.GetConfigStructureGradeDropdown(c)
	})

	route.Get("asset-types-by-auction", func(c *fiber.Ctx) error {
		return h.GetAssetTypeDropdownInAuction(c)
	})

	route.Get("carPaint", func(c *fiber.Ctx) error {
		return h.GetMasterCarPaintDropdown(c)
	})

	route.Get("void-reason", func(c *fiber.Ctx) error {
		return h.GetMasterVoidReasonDropdown(c)
	})

	route.Get("additional-service-status", func(c *fiber.Ctx) error {
		return h.GetAdditionalServiceStatusDropdown(c)
	})

	route.Get("payment-verify-method", func(c *fiber.Ctx) error {
		return h.GetPaymentVerifyMethodDropdown(c)
	})

	route.Get("automatic-refund", func(c *fiber.Ctx) error {
		return h.GetAutomaticRefundDropdown(c)
	})
	route.Get("payment-timing-mode", func(c *fiber.Ctx) error {
		return h.GetPaymentTimingModeDropdown(c)
	})
}
