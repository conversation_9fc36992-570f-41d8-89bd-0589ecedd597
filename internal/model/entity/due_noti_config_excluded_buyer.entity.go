package entity

import (
	"backend-common-lib/model"

	"gorm.io/gorm"
)

type DueNotiConfigExcludedBuyer struct {
	ID              int                 `column:"id" json:"id" example:"1"`
	DueNotiConfigId int                 `column:"payment_due_notification_id" json:"paymentDueNotificationId" example:"1"`
	BuyerId         int                 `column:"buyer_id" json:"buyerId" example:"1"`
	DeletedDate     *gorm.DeletedAt     `gorm:"column:deleted_date" json:"deletedDate"`
	Buyer           *model.BuyerForJoin `gorm:"foreignKey:BuyerId"`
}

func (DueNotiConfigExcludedBuyer) TableName() string {
	return "due_noti_config_excluded_buyer"
}
