package repository

import (
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

func (r *buyerEditProfileRequestCurrentRepositoryImpl) GetByBuyerEditProfileRequestId(buyerEditProfileRequestId int) (entity.BuyerEditProfileRequestCurrent, error) {
	var buyerEditProfileRequestCurrent entity.BuyerEditProfileRequestCurrent
	err := r.DB.Table("buyer_edit_profile_request_current").Select(
		`buyer_edit_profile_request_current.*,
		pa.description_th AS prefix_name_th,
		pa.description_en AS prefix_name_en,
		sd.description_th AS sub_district_description_th,
		sd.description_en AS sub_district_description_en,
		d.description_th AS district_description_th,
		d.description_en AS district_description_en,
		p.description_th AS province_description_th,
		p.description_en AS province_description_en,
		c.description_th AS country_description_th,
		c.description_en AS country_description_en`).
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_prefix_name) AS pa ON pa.id = buyer_edit_profile_request_current.prefix_name_id").
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_sub_district) AS sd ON sd.id = buyer_edit_profile_request_current.sub_district_id").
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_district) AS d ON d.id = buyer_edit_profile_request_current.district_id").
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_city) AS p ON p.id = buyer_edit_profile_request_current.province_id").
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_country) AS c ON c.id = buyer_edit_profile_request_current.country_id").
		Where("buyer_edit_profile_request_current.buyer_edit_profile_request_id = ?", buyerEditProfileRequestId).First(&buyerEditProfileRequestCurrent).Error
	return buyerEditProfileRequestCurrent, err
}

func (r *buyerEditProfileRequestCurrentRepositoryImpl) Insert(entity entity.BuyerEditProfileRequestCurrent) error {
	err := r.DB.Create(&entity).Error
	return err
}

func (r *buyerEditProfileRequestCurrentRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
