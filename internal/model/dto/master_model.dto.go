package dto

import (
	"backend-common-lib/model"
	"time"
)

type MasterModelDto struct {
	model.BaseDto
	BrandCode      *string `json:"brandCode"`
	ModelCode      *string `json:"modelCode"`
	Description    *string `json:"description"`
	AssetTypeCode  *string `json:"assetTypeCode"`
	IsActive       bool    `json:"isActive"`
	CompanyCode    *string `json:"companyCode"`
	IsDeletedByErp bool    `json:"isDeletedByErp"`
}

type MasterModelPageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate" example:"2020-01-01"`
}

type MasterModelSyncErpRespDto struct {
	BrandCode     *string `json:"Brand_Code"`
	ModelCode     *string `json:"Model_Code"`
	Description   *string `json:"Description"`
	AssetTypeCode *string `json:"Asset_Type_Code"`
	CompanyCode   *string `json:"CompanyCode"`
	Status        string  `json:"Status"`
}

type MasterModelListDto struct {
	MasterModelList []MasterModelDto `json:"modelMasterList"`
}
