package service

import (
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/notification_config"
)

type notificationConfigService struct {
	Repo repository.NotificationConfigRepository
}

type NotificationConfigService interface {
	GetNotificationConfig(userType string) ([]*dto.NotificationConfigDto, error)
	UpdateNotificationConfig(req dto.NotificationConfigReqDto, reqConsentType string) error
}

func NewNotificationConfigService(repo repository.NotificationConfigRepository) NotificationConfigService {
	return &notificationConfigService{Repo: repo}
}
