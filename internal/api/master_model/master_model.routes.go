package mastermodel

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_model"
	service "content-service/internal/service/master_model"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewMasterModelRepository(db)
	service := service.NewMasterModelService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("models")
	route.Post("/sync", h.SyncMasterModelFromErp)
}
