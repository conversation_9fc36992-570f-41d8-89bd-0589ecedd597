package member

import (
	buyerRepository "content-service/internal/repository/buyer"
	buyerAddressRepository "content-service/internal/repository/buyer_address"
	buyerEditProfileRequestRepository "content-service/internal/repository/buyer_edit_profile_request"
	buyerEditProfileRequestCurrentRepository "content-service/internal/repository/buyer_edit_profile_request_current"
	buyerEditProfileRequestPastRepository "content-service/internal/repository/buyer_edit_profile_request_past"
	service "content-service/internal/service/buyer_edit_profile_request"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB) {
	buyerRepo := buyerRepository.NewBuyerRepository(db)
	buyerEditProfileRequestRepo := buyerEditProfileRequestRepository.NewBuyeEditProfileRequestRepository(db)
	buyerEditProfileRequestCurrentRepo := buyerEditProfileRequestCurrentRepository.NewBuyeEditProfileRequestCurrentRepository(db)
	buyerEditProfileRequestPastRepo := buyerEditProfileRequestPastRepository.NewBuyeEditProfileRequestPastRepository(db)
	buyerAddressRepo := buyerAddressRepository.NewBuyeAddressRepository(db)
	service := service.NewBuyerEditprofileRequestService(buyerRepo, buyerEditProfileRequestRepo, buyerEditProfileRequestCurrentRepo, buyerEditProfileRequestPastRepo, buyerAddressRepo)
	handler := &Handler{Service: service}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("buyer-edit-profile-request")
	route.Post("/search", h.FindBuyerEditProfileWithFilter)
	route.Post("/masking/search", h.FindBuyerEditProfileWithFilterMasking)
	route.Put("/status/:id", h.UpdateBuyerEditProfileRequestStatus)
	route.Get("/:id", h.GetBuyerEditProfileById)
	route.Get("/masking/:id", h.GetBuyerEditProfileByIdMasking)
	route.Get("/log/:id", h.GetBuyerEditProfileLogById)
	route.Get("/masking/log/:id", h.GetBuyerEditProfileLogByIdMasking)
}
