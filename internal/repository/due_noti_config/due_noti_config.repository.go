package repository

import (
	"backend-common-lib/model"
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type dueNotiConfigRepositoryImpl struct {
	DB *gorm.DB
}

type DueNotiConfigRepository interface {
	FindAllDueNotiConfig(req model.PagingRequest, notiType string) ([]entity.DueNotiConfig, error)
	CountAllDueNotiConfig(notiType string) (int64, error)

	FindDueNotiConfigByID(id int) (*entity.DueNotiConfig, error)

	InsertDueNotiConfig(entityPaymentDue *entity.DueNotiConfig) error
	FindByCustomerGroupId(customerGroupId int, notiType string) ([]entity.DueNotiConfig, error)

	UpdateDueNotiConfig(id int, fieldToUpdate map[string]interface{}) (int64, error)

	DeleteDueNotiConfig(id int) (int64, error)

	GetDB() *gorm.DB
}

func NewDueNotiConfigRepository(db *gorm.DB) DueNotiConfigRepository {
	return &dueNotiConfigRepositoryImpl{DB: db}
}
