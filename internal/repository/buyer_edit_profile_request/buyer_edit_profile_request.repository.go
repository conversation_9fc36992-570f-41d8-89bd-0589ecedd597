package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type buyerEditProfileRequestRepositoryImpl struct {
	DB *gorm.DB
}

type BuyerEditProfileRequestRepository interface {
	FindBuyerEditProfileWithFilter(req dto.BuyerEditProfileSearchReqDto) ([]entity.BuyerEditProfileRequest, error)
	CountBuyerEditProfileWithFilter(req dto.BuyerEditProfileSearchReqDto) (int64, error)
	GetById(id int) (entity.BuyerEditProfileRequest, error)
	GetAllByIds(ids []int) ([]*entity.BuyerEditProfileRequest, error)
	GetAllBuyerPastByIds(ids []int) ([]*entity.BuyerEditProfileRequestPast, error)
	UpdateFields(buyerEditProfileRequestId int, fields map[string]interface{}) error

	GetDB() *gorm.DB
}

func NewBuyeEditProfileRequestRepository(db *gorm.DB) BuyerEditProfileRequestRepository {
	return &buyerEditProfileRequestRepositoryImpl{DB: db}
}
