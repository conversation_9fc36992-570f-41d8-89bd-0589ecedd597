package service

import (
	"backend-common-lib/constant"
	"backend-common-lib/erp"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_asset_location"
	"fmt"
	"net/http"

	"time"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (s *masterAssetLocationService) SearchMasterAssetLocationFilter(req dto.MasterAssetLocationPageReqDto) (dto.MasterAssetLocationPageRespDto[dto.MasterAssetLocationDto], error) {
	resp := dto.MasterAssetLocationPageRespDto[dto.MasterAssetLocationDto]{}
	result, err := s.Repo.FindMasterAssetLocationWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountMasterAssetLocationWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.MasterAssetLocationDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.MasterAssetLocationDto](v)
	}

	//NOTE Get latest sync date
	var latestSyncDate *time.Time
	if len(result) > 0 {
		latestSyncDate = result[0].LatestSyncDate
	} else {
		latestSyncDate, err = s.Repo.FindMasterAssetLocationLatestSyncDate()
		if err != nil {
			return resp, errs.NewError(http.StatusInternalServerError, err)
		}
	}

	//NOTE - Response
	resp = dto.MasterAssetLocationPageRespDto[dto.MasterAssetLocationDto]{
		PagingModel:    *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
		LatestSyncDate: latestSyncDate,
	}

	return resp, nil
}

func (s *masterAssetLocationService) UpdateMasterAssetLocationStatus(req dto.MasterAssetLocationUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdatesMasterAssetLocationFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}

func (s *masterAssetLocationService) SyncMasterAssetLocationFromErp(actionBy *int, erpConfig global.ErpConfig) error {
	erpMap, allKeys, err := s.getAssetLocationFromErp(erpConfig)
	if err != nil {
		return err
	}
	dbMap, err := s.getAssetLocationFromDb(allKeys)
	if err != nil {
		return err
	}
	err = s.syncAssetLocation(actionBy, erpMap, dbMap, allKeys)
	if err != nil {
		return err
	}

	return nil
}

func (s *masterAssetLocationService) getAssetLocationFromErp(erpConfig global.ErpConfig) (map[string]dto.MasterAssetLocationDto, map[string]struct{}, error) {
	erpList, err := erp.FetchListFromErp[dto.MasterAssetLocationSyncErpRespDto](
		erpConfig.AssetLocationUrl,
		erpConfig.LoginUrl,
		erpConfig.Email,
		erpConfig.Password,
		map[string]interface{}{"Company_Name": "AUCT"},
		"data",
	)
	if err != nil {
		return nil, nil, errs.NewError(http.StatusInternalServerError, err)
	}

	erpMap := make(map[string]dto.MasterAssetLocationDto)
	allKeys := make(map[string]struct{})

	for _, e := range erpList {
		erpMap[util.Val(e.LocationCode)] = dto.MasterAssetLocationDto{
			CompanyCode:    e.CompanyCode,
			LocationCode:   e.LocationCode,
			DescriptionTh:  e.DescriptionTh,
			DescriptionEn:  e.DescriptionEn,
			BranchCode:     e.BranchCode,
			IsSaleLocation: e.IsSaleLocation,
			CountryCode:    e.CountryCode,
			RegionCode:     e.RegionCode,
			CityCode:       e.CityCode,
			PostCode:       e.PostCode,
			IsActive:       e.Status == "Active",
		}
		allKeys[util.Val(e.LocationCode)] = struct{}{}
	}
	return erpMap, allKeys, nil
}

func (s *masterAssetLocationService) getAssetLocationFromDb(allKeys map[string]struct{}) (map[string]entity.MasterAssetLocation, error) {
	dbList, err := s.Repo.FindMasterAssetLocationAll()
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	dbMap := make(map[string]entity.MasterAssetLocation)
	for _, e := range dbList {
		dbMap[util.Val(e.LocationCode)] = e
		allKeys[util.Val(e.LocationCode)] = struct{}{}
	}
	return dbMap, nil
}

func (s *masterAssetLocationService) syncAssetLocation(actionBy *int, erpMap map[string]dto.MasterAssetLocationDto, dbMap map[string]entity.MasterAssetLocation, allKeys map[string]struct{}) error {
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		repo := repository.NewMasterAssetLocationRepository(tx)
		var toInsert []entity.MasterAssetLocation
		currentDateTime := util.NowPtr()

		for code := range allKeys {
			erp, erpExists := erpMap[code]
			db, dbExists := dbMap[code]

			if erpExists && dbExists {
				// มีทั้ง ERP + DB → compare → update หมด
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.CompanyCode = erp.CompanyCode
				temp.LocationCode = erp.LocationCode
				temp.DescriptionTh = erp.DescriptionTh
				temp.DescriptionEn = erp.DescriptionEn
				temp.BranchCode = erp.BranchCode
				temp.IsSaleLocation = erp.IsSaleLocation
				temp.CountryCode = erp.CountryCode
				temp.RegionCode = erp.RegionCode
				temp.CityCode = erp.CityCode
				temp.PostCode = erp.PostCode
				temp.IsActive = erp.IsActive
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterAssetLocationAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}

			} else if erpExists && !dbExists {
				// มีใน ERP แต่ไม่มีใน DB → insert
				temp := entity.MasterAssetLocation{
					BaseEntity: &model.BaseEntity{
						CreatedDate: *currentDateTime,
						CreatedBy:   actionBy,
						UpdatedDate: currentDateTime,
						UpdatedBy:   actionBy,
					},
					CompanyCode:    erp.CompanyCode,
					LocationCode:   erp.LocationCode,
					DescriptionTh:  erp.DescriptionTh,
					DescriptionEn:  erp.DescriptionEn,
					BranchCode:     erp.BranchCode,
					IsSaleLocation: erp.IsSaleLocation,
					CountryCode:    erp.CountryCode,
					RegionCode:     erp.RegionCode,
					CityCode:       erp.CityCode,
					PostCode:       erp.PostCode,
					IsActive:       erp.IsActive,
					IsDeletedByErp: false,
					LatestSyncDate: currentDateTime,
				}
				toInsert = append(toInsert, temp)

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterAssetLocationAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}
		}

		if len(toInsert) > 0 {
			if err := repo.InsertMasterAssetLocationList(toInsert); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}
		return nil
	})
}
