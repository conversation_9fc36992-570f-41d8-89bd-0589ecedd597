package entity

import (
	"backend-common-lib/model"
	"time"

	"gorm.io/gorm"
)

type LotSouLotLine struct {
	*model.BaseEntity
	LotId                          int                     `column:"lot_id" json:"lotId"`
	LotSouId                       *int                    `column:"lot_sou_id" json:"lotSouId"`
	AuctionDate                    *time.Time              `column:"auction_date" json:"auctionDate"`
	DocumentNo                     *string                 `column:"document_no" json:"documentNo"`
	AssetCode                      *string                 `column:"asset_code" json:"assetCode"`
	Floor                          *string                 `column:"floor" json:"floor"`
	AuctCode                       *string                 `column:"auct_code" json:"auctCode"`
	AuctionNo                      *int                    `column:"auction_no" json:"auctionNo"`
	CityCode                       *string                 `column:"city_code" json:"cityCode"`
	AssetGroupCode                 *string                 `column:"asset_group_code" json:"assetGroupCode"`
	BrandDescription               *string                 `column:"brand_description" json:"brandDescription"`
	ModelDescription               *string                 `column:"model_description" json:"modelDescription"`
	Model                          *string                 `column:"model" json:"model"`
	SubModel                       *string                 `column:"sub_model" json:"sub_Model"`
	SubKey                         *string                 `column:"sub_key" json:"sub_Key"`
	EngineSize                     *string                 `column:"engine_size" json:"engineSize"`
	LicensePlateNo                 *string                 `column:"license_plate_no" json:"licensePlateNo"`
	CityDescription                *string                 `column:"city_description" json:"cityDescription"`
	RegistrationYear               *string                 `column:"registration_year" json:"registrationYear"`
	YearOfManufacture              *string                 `column:"year_of_manufacture" json:"yearOfManufacture"`
	ColorInCopy                    *string                 `column:"color_in_copy" json:"colorInCopy"`
	TaxDate                        *time.Time              `column:"tax_date" json:"taxDate"`
	VendorGroup                    *string                 `column:"vendor_group" json:"vendorGroup"`
	Mile                           *int                    `column:"mile" json:"mile"`
	SalesPrice                     *float64                `column:"sales_price" json:"salesPrice"`
	SoldAmount                     *float64                `column:"sold_amount" json:"soldAmount"`
	VatPercentage                  *float64                `column:"vat_percentage" json:"vatPercentage"`
	AssetYear                      *int                    `column:"asset_year" json:"assetYear"`
	BranchDescription              *string                 `column:"branch_description" json:"branchDescription"`
	ContractNo                     *string                 `column:"contract_no" json:"contractNo"`
	ChassisNo                      *string                 `column:"chassis_no" json:"chassisNo"`
	EngineNo                       *string                 `column:"engine_no" json:"engineNo"`
	AssetType                      *string                 `column:"asset_type" json:"assetType"`
	BranchCode                     *string                 `column:"branch_code" json:"branchCode"`
	BranchDescTh                   *string                 `gorm:"column:branch_desc_th;->" json:"branchDescTh"`
	BranchDescEn                   *string                 `gorm:"column:branch_desc_en;->" json:"branchDescEn"`
	SellerCode                     *string                 `column:"seller_code" json:"sellerCode"`
	SellerName                     *string                 `column:"seller_name" json:"sellerName"`
	FuelTap                        *string                 `column:"fuel_tap" json:"fuelTap"`
	Gear                           *string                 `column:"gear" json:"gear"`
	GearName                       *string                 `column:"gear_name" json:"gear_Name"`
	Unque                          *string                 `column:"unque" json:"unque"`
	CarTypeCon                     *string                 `column:"car_type_con" json:"car_Type_con"`
	PointGrade1Final               *int                    `column:"point_grade1_final" json:"pointGrade1Final"`
	PointGrade2Con                 *int                    `column:"point_grade2_con" json:"pointGrade2Con"`
	PointGrade3Con                 *int                    `column:"point_grade3_con" json:"pointGrade3Con"`
	FinanceZipFilename             *string                 `column:"finance_zip_filename" json:"financeZipFilename"`
	SellerOffer                    *string                 `column:"seller_offer" json:"sellerOffer"`
	SellerOfferDescTh              *string                 `gorm:"column:seller_offer_desc_th;->" json:"sellerOfferDescTh"`
	SellerOfferDescEn              *string                 `gorm:"column:seller_offer_desc_en;->" json:"sellerOfferDescEn"`
	AuctionTime                    *time.Time              `column:"auction_time" json:"auctionTime"`
	RefLicensePlate                *string                 `column:"ref_license_plate" json:"refLicensePlate"`
	Insurance                      *string                 `column:"insurance" json:"insurance"`
	FeeType                        *string                 `column:"fee_type" json:"feeType"`
	FeeAmount                      *float64                `column:"fee_amount" json:"feeAmount"`
	ToyTaxAmt                      *float64                `column:"toy_tax_amt" json:"toyTaxAmt"`
	SalesType                      *string                 `column:"sales_type" json:"salesType"`
	PersonalSale                   *string                 `column:"personal_sale" json:"personalSale"`
	PersonalSaleDate               *time.Time              `column:"personal_sale_date" json:"personalSaleDate"`
	StaffRemark2                   *string                 `column:"staff_remark2" json:"staffRemark2"`
	StaffRemark2Date               *time.Time              `column:"staff_remark2_date" json:"staffRemark2Date"`
	AssetImportCost                *string                 `column:"asset_import_cost" json:"assetImportCost"`
	AssetImportCostDate            *time.Time              `column:"asset_import_cost_date" json:"assetImportCostDate"`
	PersonalCheckConditionSale     *string                 `column:"personal_check_condition_sale" json:"personalCheckConditionSale"`
	PersonalCheckConditionSaleDate *time.Time              `column:"personal_check_condition_sale_date" json:"personalCheckConditionSaleDate"`
	SpecialAuction                 *string                 `column:"special_auction" json:"specialAuction"`
	SpecialAuctionDate             *time.Time              `column:"special_auction_date" json:"specialAuctionDate"`
	PersonalSaleNote               *string                 `column:"personal_sale_note" json:"personalSaleNote"`
	PersonalSaleNoteDate           *time.Time              `column:"personal_sale_note_date" json:"personalSaleNoteDate"`
	PersonalAssessmentAuction      *string                 `column:"personal_assessment_auction" json:"personalAssessmentAuction"`
	PersonalAssessmentAuctionDate  *time.Time              `column:"personal_assessment_auction_date" json:"personalAssessmentAuctionDate"`
	ToolBike                       *string                 `column:"tool_bike" json:"toolBike"`
	ToolCar                        *string                 `column:"tool_car" json:"toolCar"`
	SaleRounds                     *int                    `column:"sale_rounds" json:"saleRounds"`
	AssessmentPlate                *string                 `column:"assessment_plate" json:"assessmentPlate"`
	LocationCode                   *string                 `column:"location_Code" json:"locationCode"`
	DescriptionTH                  *string                 `column:"description_Th" json:"descriptionTH"`
	AuctionStatus                  *string                 `column:"auction_status" json:"auctionStatus"`
	ProductStatus                  *string                 `column:"product_status" json:"productStatus"`
	FirstImageUrl                  *string                 `column:"first_image_url" json:"firstImageUrl"`
	IsActive                       bool                    `column:"is_active" json:"isActive"`
	AuctionTag                     *string                 `column:"auction_tag" json:"auctionTag"`
	IsDeletedByErp                 bool                    `column:"is_deleted_by_erp" json:"isDeletedByErp"`
	LatestSyncDate                 *time.Time              `column:"latest_sync_date" json:"latestSyncDate"`
	DeletedDate                    *gorm.DeletedAt         `gorm:"column:deleted_date" json:"deletedDate"`
	MasterAssetType                *model.AssetTypeForJoin `gorm:"foreignKey:AssetType;references:AssetTypeCode;->"`
	CreatedUser                    *model.EmployeeForJoin  `gorm:"foreignKey:CreatedBy;references:UserID;->"`
	UpdatedUser                    *model.EmployeeForJoin  `gorm:"foreignKey:UpdatedBy;references:UserID;->"`
	// LotSouLotLineCostListToInserts []LotSouLotLineCost     `gorm:"-"`
	// LotSouLotLineCostListToUpdates []LotSouLotLineCost     `gorm:"-"`

	Tool     *string    `column:"tool" json:"tool"`
	SoldDate *time.Time `column:"sold_date" json:"soldDate"`
	SoldTime *string    `column:"sold_time" json:"soldTime"`
	Remark   *string    `column:"remark" json:"remark"`
}

func (LotSouLotLine) TableName() string {
	return "lot_sou_lot_line"
}
