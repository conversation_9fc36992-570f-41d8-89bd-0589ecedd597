package repository

import (
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type registrationBuyerFormConfigRepositoryImpl struct {
	DB *gorm.DB
}

type RegistrationBuyerFormConfigRepository interface {
	FindRegistrationBuyerFormConfig() (*entity.RegistrationBuyerFormConfig, error)
	UpdatesRegistrationBuyerFormConfigFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)
}

func NewRegistrationBuyerFormConfigRepository(db *gorm.DB) RegistrationBuyerFormConfigRepository {
	return &registrationBuyerFormConfigRepositoryImpl{DB: db}
}
