package repository

import (
	commonConstant "backend-common-lib/constant"
	"backend-common-lib/util"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

func (r *passwordExpiryRepositoryImpl) FindAllPasswordExpiry() ([]entity.PasswordExpiry, error) {
	var results []entity.PasswordExpiry

	query := r.DB.Model(&entity.PasswordExpiry{})
	query = util.JoinUsers("password_expiry")(query)

	query.Order("user_type asc")

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *passwordExpiryRepositoryImpl) UpdatesPasswordExpiryFieldsWhere(fields map[string]interface{}, id int) (int64, *entity.PasswordExpiry, error) {
	var updatedRecord entity.PasswordExpiry
	err := r.DB.Transaction(func(tx *gorm.DB) error {
		result := tx.Model(&entity.PasswordExpiry{}).Where("id = ?", id).Updates(fields).Scan(&updatedRecord)
		if result.Error != nil {
			return result.Error
		}

		if updatedRecord.UserType == nil {
			return gorm.ErrInvalidData
		}

		var userTypeId []int
		switch *updatedRecord.UserType {
		case "BUYER":
			userTypeId = append(userTypeId, commonConstant.UserTypeBuyer)
		case "SELLER":
			userTypeId = append(userTypeId, commonConstant.UserTypeSeller)
		case "EMPLOYEE":
			userTypeId = append(userTypeId, commonConstant.UserTypeAuctioneer, commonConstant.UserTypeController, commonConstant.UserTypeSystemAdmin)

		default:
			return gorm.ErrInvalidData
		}

		var expiredDate *time.Time
		if updatedRecord.PasswordExpiryDays != nil {
			expiredDate = util.Ptr(time.Now().Add(time.Duration(*updatedRecord.PasswordExpiryDays) * 24 * time.Hour))
		}

		query := `
			UPDATE users
			SET password_expired_date = ?
			FROM user_roles ur
			JOIN roles r ON ur.role_id = r.id
			WHERE users.id = ur.user_id
				AND ur.role_id IN ?
		`
		result = tx.Exec(query, &expiredDate, userTypeId)
		if result.Error != nil {
			return result.Error
		}
		return nil
	})

	if err != nil {
		return 0, nil, err
	}

	return 1, &updatedRecord, nil
}

func (r *passwordExpiryRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
