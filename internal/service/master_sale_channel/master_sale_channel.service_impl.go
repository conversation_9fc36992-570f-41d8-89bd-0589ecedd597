package service

import (
	"backend-common-lib/erp"
	"content-service/internal/global"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_sale_channel"
	"fmt"
	"net/http"

	"time"

	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (s *masterSaleChannelService) SearchMasterSaleChannelFilter(req dto.MasterSaleChannelPageReqDto) (dto.MasterSaleChannelPageRespDto[dto.MasterSaleChannelDto], error) {
	resp := dto.MasterSaleChannelPageRespDto[dto.MasterSaleChannelDto]{}
	result, err := s.Repo.FindMasterSaleChannelWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountMasterSaleChannelWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.MasterSaleChannelDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.MasterSaleChannelDto](v)
	}

	//NOTE Get latest sync date
	var latestSyncDate *time.Time
	if len(result) > 0 {
		latestSyncDate = result[0].LatestSyncDate
	} else {
		latestSyncDate, err = s.Repo.FindMasterSaleChannelLatestSyncDate()
		if err != nil {
			return resp, errs.NewError(http.StatusInternalServerError, err)
		}
	}

	//NOTE - Response
	resp = dto.MasterSaleChannelPageRespDto[dto.MasterSaleChannelDto]{
		PagingModel:    *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
		LatestSyncDate: latestSyncDate,
	}

	return resp, nil
}

func (s *masterSaleChannelService) UpdateMasterSaleChannelStatus(req dto.MasterSaleChannelUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdatesMasterSaleChannelFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}

func (s *masterSaleChannelService) SyncMasterSaleChannelFromErp(actionBy *int, erpConfig global.ErpConfig) error {
	erpMap, allKeys, err := s.getSaleChannelFromErp(erpConfig)
	if err != nil {
		return err
	}
	dbMap, err := s.getSaleChannelFromDb(allKeys)
	if err != nil {
		return err
	}
	err = s.syncSaleChannel(actionBy, erpMap, dbMap, allKeys)
	if err != nil {
		return err
	}

	return nil
}

func (s *masterSaleChannelService) getSaleChannelFromErp(erpConfig global.ErpConfig) (map[string]dto.MasterSaleChannelDto, map[string]struct{}, error) {
	erpList, err := erp.FetchListFromErp[dto.MasterSaleChannelSyncErpRespDto](
		erpConfig.SaleChannelUrl,
		erpConfig.LoginUrl,
		erpConfig.Email,
		erpConfig.Password,
		map[string]interface{}{"Company_Name": "AUCT"},
		"data",
	)
	if err != nil {
		return nil, nil, errs.NewError(http.StatusInternalServerError, err)
	}

	erpMap := make(map[string]dto.MasterSaleChannelDto)
	allKeys := make(map[string]struct{})

	for _, e := range erpList {
		erpMap[util.Val(e.SaleChannelCode)] = dto.MasterSaleChannelDto{
			CompanyCode:                       e.CompanyCode,
			SaleChannelCode:                   e.SaleChannelCode,
			DescriptionTh:                     e.DescriptionTh,
			DescriptionEn:                     e.DescriptionEn,
			AuctionExcludeValidateReturnBoard: e.AuctionExcludeValidateReturnBoard,
			AllowRefund:                       e.AllowRefund,
			IsActive:                          e.Status == "Active",
		}
		allKeys[util.Val(e.SaleChannelCode)] = struct{}{}
	}
	return erpMap, allKeys, nil
}

func (s *masterSaleChannelService) getSaleChannelFromDb(allKeys map[string]struct{}) (map[string]entity.MasterSaleChannel, error) {
	dbList, err := s.Repo.FindMasterSaleChannelAll()
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	dbMap := make(map[string]entity.MasterSaleChannel)
	for _, e := range dbList {
		dbMap[util.Val(e.SaleChannelCode)] = e
		allKeys[util.Val(e.SaleChannelCode)] = struct{}{}
	}
	return dbMap, nil
}

func (s *masterSaleChannelService) syncSaleChannel(actionBy *int, erpMap map[string]dto.MasterSaleChannelDto, dbMap map[string]entity.MasterSaleChannel, allKeys map[string]struct{}) error {
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		repo := repository.NewMasterSaleChannelRepository(tx)
		var toInsert []entity.MasterSaleChannel
		currentDateTime := util.NowPtr()

		for code := range allKeys {
			erp, erpExists := erpMap[code]
			db, dbExists := dbMap[code]

			if erpExists && dbExists {
				// มีทั้ง ERP + DB → compare → update หมด
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.CompanyCode = erp.CompanyCode
				temp.SaleChannelCode = erp.SaleChannelCode
				temp.DescriptionTh = erp.DescriptionTh
				temp.DescriptionEn = erp.DescriptionEn
				temp.AuctionExcludeValidateReturnBoard = erp.AuctionExcludeValidateReturnBoard
				temp.AllowRefund = erp.AllowRefund
				temp.IsActive = erp.IsActive
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterSaleChannelAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}

			} else if erpExists && !dbExists {
				// มีใน ERP แต่ไม่มีใน DB → insert
				temp := entity.MasterSaleChannel{
					BaseEntity: &model.BaseEntity{
						CreatedDate: *currentDateTime,
						CreatedBy:   actionBy,
						UpdatedDate: currentDateTime,
						UpdatedBy:   actionBy,
					},
					CompanyCode:                       erp.CompanyCode,
					SaleChannelCode:                   erp.SaleChannelCode,
					DescriptionTh:                     erp.DescriptionTh,
					DescriptionEn:                     erp.DescriptionEn,
					AuctionExcludeValidateReturnBoard: erp.AuctionExcludeValidateReturnBoard,
					AllowRefund:                       erp.AllowRefund,
					IsActive:                          erp.IsActive,
					IsDeletedByErp:                    false,
					LatestSyncDate:                    currentDateTime,
				}
				toInsert = append(toInsert, temp)

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterSaleChannelAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}
		}

		if len(toInsert) > 0 {
			if err := repo.InsertMasterSaleChannelList(toInsert); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}
		return nil
	})
}
