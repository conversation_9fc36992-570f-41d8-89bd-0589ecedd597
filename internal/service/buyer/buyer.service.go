package service

import (
	"content-service/internal/model/dto"
	buyerRepository "content-service/internal/repository/buyer"
)

type buyerService struct {
	BuyerRepo buyerRepository.BuyerRepository
}

type BuyerService interface {
	FindBuyerDetailsWithFilter(req dto.BuyerSearchReqDto) (dto.BuyerSearchRespDto, error)
}

func NewBuyerService(buyer buyerRepository.BuyerRepository) BuyerService {
	return &buyerService{BuyerRepo: buyer}
}
