package service

import (
	"backend-common-lib/constant"
	"backend-common-lib/erp"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_postcode"
	"fmt"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (s *masterPostcodeService) SearchMasterPostcodeFilter(req dto.MasterPostcodePageReqDto) (dto.MasterPostcodePageRespDto[dto.MasterPostcodeDto], error) {
	resp := dto.MasterPostcodePageRespDto[dto.MasterPostcodeDto]{}
	result, err := s.Repo.FindMasterPostcodeWithFilter(req)
	if err != nil {
		return resp, errs.NewError(fiber.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountMasterPostcodeWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.MasterPostcodeDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.MasterPostcodeDto](v)
	}

	//NOTE Get latest sync date
	var latestSyncDate *time.Time
	if len(result) > 0 {
		latestSyncDate = result[0].LatestSyncDate
	} else {
		latestSyncDate, err = s.Repo.FindLatestSyncDate()
		if err != nil {
			return resp, errs.NewError(fiber.StatusInternalServerError, err)
		}
	}

	//NOTE - Response
	resp = dto.MasterPostcodePageRespDto[dto.MasterPostcodeDto]{
		PagingModel:    *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
		LatestSyncDate: latestSyncDate,
	}

	return resp, nil
}

func (s *masterPostcodeService) UpdateMasterPostcodeStatus(req dto.MasterPostcodeUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdatesMasterPostcodeFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}

func (s *masterPostcodeService) SyncMasterPostcodeFromErp(actionBy *int, erpConfig global.ErpConfig) error {
	erpMap, allKeys, err := s.getPostcodeFromErp(erpConfig)
	if err != nil {
		return err
	}
	dbMap, err := s.getPostcodeFromDb(allKeys)
	if err != nil {
		return err
	}
	err = s.syncPostcode(actionBy, erpMap, dbMap, allKeys)
	if err != nil {
		return err
	}

	return nil
}

func (s *masterPostcodeService) getPostcodeFromErp(erpConfig global.ErpConfig) (map[string]dto.MasterPostcodeDto, map[string]struct{}, error) {
	erpList, err := erp.FetchListFromErp[dto.MasterPostcodeSyncErpRespDto](
		erpConfig.PostcodeUrl,
		erpConfig.LoginUrl,
		erpConfig.Email,
		erpConfig.Password,
		map[string]interface{}{"Company_Name": "AUCT"},
		"data",
	)
	if err != nil {
		return nil, nil, errs.NewError(http.StatusInternalServerError, err)
	}

	erpMap := make(map[string]dto.MasterPostcodeDto)
	allKeys := make(map[string]struct{})

	for _, e := range erpList {
		postcodeStr := util.Val(e.SubDistrictCode) + util.Val(e.PostCode)
		erpMap[postcodeStr] = dto.MasterPostcodeDto{
			CountryCode:     e.CountryCode,
			RegionCode:      e.RegionCode,
			CityCode:        e.CityCode,
			DistrictCode:    e.DistrictCode,
			SubDistrictCode: e.SubDistrictCode,
			PostCode:        e.PostCode,
			DescriptionTh:   e.DescriptionTh,
			DescriptionEn:   e.DescriptionEn,
			IsActive:        e.Status == "Active",
		}
		allKeys[postcodeStr] = struct{}{}
	}
	return erpMap, allKeys, nil
}

func (s *masterPostcodeService) getPostcodeFromDb(allKeys map[string]struct{}) (map[string]entity.MasterPostcode, error) {
	dbList, err := s.Repo.FindMasterPostcodeAll()
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	dbMap := make(map[string]entity.MasterPostcode)
	for _, e := range dbList {
		postcodeStr := util.Val(e.SubDistrictCode) + util.Val(e.PostCode)
		dbMap[postcodeStr] = e
		allKeys[postcodeStr] = struct{}{}
	}
	return dbMap, nil
}

func (s *masterPostcodeService) syncPostcode(actionBy *int, erpMap map[string]dto.MasterPostcodeDto, dbMap map[string]entity.MasterPostcode, allKeys map[string]struct{}) error {
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		repo := repository.NewMasterPostcodeRepository(tx)
		var toInsert []entity.MasterPostcode
		currentDateTime := util.NowPtr()

		for code := range allKeys {
			erp, erpExists := erpMap[code]
			db, dbExists := dbMap[code]

			if erpExists && dbExists {
				// มีทั้ง ERP + DB → compare → update หมด
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.CountryCode = erp.CountryCode
				temp.RegionCode = erp.RegionCode
				temp.CityCode = erp.CityCode
				temp.DistrictCode = erp.DistrictCode
				temp.SubDistrictCode = erp.SubDistrictCode
				temp.PostCode = erp.PostCode
				temp.DescriptionTh = erp.DescriptionTh
				temp.DescriptionEn = erp.DescriptionEn
				temp.IsActive = erp.IsActive
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = currentDateTime

				if changes := util.CompareStructDeep(db, temp, ""); len(changes) > 0 {
					err := repo.UpdateMasterPostcodeAllFields(&temp)
					if err != nil {
						return errs.NewError(fiber.StatusInternalServerError, err)
					}
				}

			} else if erpExists && !dbExists {
				// มีใน ERP แต่ไม่มีใน DB → insert
				temp := entity.MasterPostcode{
					BaseEntity: &model.BaseEntity{
						CreatedDate: *currentDateTime,
						CreatedBy:   actionBy,
						UpdatedDate: currentDateTime,
						UpdatedBy:   actionBy,
					},
					CountryCode:     erp.CountryCode,
					RegionCode:      erp.RegionCode,
					CityCode:        erp.CityCode,
					DistrictCode:    erp.DistrictCode,
					SubDistrictCode: erp.SubDistrictCode,
					PostCode:        erp.PostCode,
					DescriptionTh:   erp.DescriptionTh,
					DescriptionEn:   erp.DescriptionEn,
					IsActive:        erp.IsActive,
					IsDeletedByErp:  false,
					LatestSyncDate:  currentDateTime,
				}
				toInsert = append(toInsert, temp)

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterPostcodeAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}
		}

		if len(toInsert) > 0 {
			if err := repo.InsertMasterPostcodeListWithBatches(toInsert); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}

		_, err := repo.UpdatesMasterPostcodeFieldsWhere(map[string]interface{}{"latest_sync_date": currentDateTime, "updated_date": currentDateTime, "updated_by": actionBy}, "is_deleted_by_erp = ?", false)
		if err != nil {
			return errs.NewError(http.StatusInternalServerError, err)
		}

		return nil
	})
}

func (s *masterPostcodeService) GetPostCodeBySubDistrictID(subDistrictID int) (*string, error) {

	masterPostCode, err := s.Repo.GetBySubDistrictID(subDistrictID)
	if err != nil {
		return nil, err
	}

	if masterPostCode != nil {
		return masterPostCode.PostCode, nil
	} else {
		return nil, nil
	}
}
