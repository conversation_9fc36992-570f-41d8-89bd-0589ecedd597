package service

import (
	"fmt"
	"log"
	"net/http"

	"backend-common-lib/erp"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_brand"

	"gorm.io/gorm"
)

func (s *masterBrandService) SyncMasterBrandFromErp(actionBy *int, erpConfig global.ErpConfig) error {
	log.Printf("start getBrandFromErp")
	erpMap, allKeys, err := s.getBrandFromErp(erpConfig)
	if err != nil {
		fmt.Printf("err getBrandFromErp : %++v", err)
		return err
	}
	log.Printf("start getBrandFromDb")
	dbMap, err := s.getBrandFromDb(allKeys)
	if err != nil {
		fmt.Printf("err getBrandFromDb : %++v", err)
		return err
	}
	log.Printf("start syncBrand")
	err = s.syncBrand(actionBy, erpMap, dbMap, allKeys)
	if err != nil {
		fmt.Printf("err syncBrand : %++v", err)
		return err
	}

	return nil
}

func (s *masterBrandService) getBrandFromErp(erpConfig global.ErpConfig) (map[string]dto.MasterBrandDto, map[string]struct{}, error) {
	erpList, err := erp.FetchListFromErp[dto.MasterBrandSyncErpRespDto](
		erpConfig.BrandUrl,
		erpConfig.LoginUrl,
		erpConfig.Email,
		erpConfig.Password,
		map[string]interface{}{"Company_Name": "AUCT"},
		"data",
	)
	if err != nil {
		return nil, nil, errs.NewError(http.StatusInternalServerError, err)
	}

	erpMap := make(map[string]dto.MasterBrandDto)
	allKeys := make(map[string]struct{})

	for _, e := range erpList {
		erpMap[util.Val(e.BrandCode)] = dto.MasterBrandDto{
			CompanyCode:   e.CompanyCode,
			AssetTypeCode: e.AssetTypeCode,
			IsActive:      e.Status == "Active",
			BrandCode:     e.BrandCode,
			Description:   e.Description,
		}
		allKeys[util.Val(e.BrandCode)] = struct{}{}
	}
	return erpMap, allKeys, nil
}

func (s *masterBrandService) getBrandFromDb(allKeys map[string]struct{}) (map[string]entity.MasterBrand, error) {
	dbList, err := s.Repo.FindMasterBrandAll()
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	dbMap := make(map[string]entity.MasterBrand)
	for _, e := range dbList {
		dbMap[util.Val(e.BrandCode)] = e
		allKeys[util.Val(e.BrandCode)] = struct{}{}
	}
	return dbMap, nil
}

func (s *masterBrandService) syncBrand(actionBy *int, erpMap map[string]dto.MasterBrandDto, dbMap map[string]entity.MasterBrand, allKeys map[string]struct{}) error {
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		repo := repository.NewMasterBrandRepository(tx)
		var toInsert []entity.MasterBrand
		currentDateTime := util.NowPtr()

		for code := range allKeys {
			erp, erpExists := erpMap[code]
			db, dbExists := dbMap[code]

			if erpExists && dbExists {
				// มีทั้ง ERP + DB → compare → update หมด
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.CompanyCode = erp.CompanyCode
				temp.AssetTypeCode = erp.AssetTypeCode
				temp.BrandCode = erp.BrandCode
				temp.Description = erp.Description
				temp.IsActive = erp.IsActive
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterBrandAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}

			} else if erpExists && !dbExists {
				// มีใน ERP แต่ไม่มีใน DB → insert
				temp := entity.MasterBrand{
					BaseEntity: &model.BaseEntity{
						CreatedDate: *currentDateTime,
						CreatedBy:   actionBy,
						UpdatedDate: currentDateTime,
						UpdatedBy:   actionBy,
					},
					CompanyCode:    erp.CompanyCode,
					AssetTypeCode:  erp.AssetTypeCode,
					BrandCode:      erp.BrandCode,
					Description:    erp.Description,
					IsActive:       erp.IsActive,
					IsDeletedByErp: false,
					LatestSyncDate: currentDateTime,
				}
				toInsert = append(toInsert, temp)

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterBrandAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}
		}

		if len(toInsert) > 0 {
			if err := repo.InsertMasterBrandList(toInsert); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}
		return nil
	})
}
