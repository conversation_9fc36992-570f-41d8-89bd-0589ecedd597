app:
  appName: "Content Service"
  domain: "content"
  httpPort: 8184
  allowOrigins: "http://localhost:4200, https://*************:4200,http://*************:8080,https://*************:4201"
  certFile: "/app/cert/preprod.pem"
  keyFile: "/app/cert/preprod-key.pem"
public:
  - "api/signin"

dbConfig:
  host: ***********
  port: 5432
  dbName: auction_core
  username: appusr
  password: "auctapp"
  schema: auction

service:
  pgwUrl:
  qrPayUrl:

redisConfig:
  host: ***********
  port: 6379
  password:
  db: 0
  protocol: 2

ErpConfig:
  token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************.BMMfNCw0dkw0Z0S0fB7Bg0O-5a8GJu7u3xBi8IwqaK8"
  eventUrl: "https://systemuat.auct.co.th:3443/api/v1/masterEvent/getEvent"
  assetGroupUrl: "https://systemuat.auct.co.th:3443/api/v1/masterAssetGroup/getAssetGroup"
  assetTypeUrl: "https://systemuat.auct.co.th:3443/api/v1/masterAssetType/getAssetType"
  saleChannelUrl: "https://systemuat.auct.co.th:3443/api/v1/masterSaleChanel/getSaleChanel"
  branchUrl: "https://systemuat.auct.co.th:3443/api/v1/masterBranch/getBranch"
  customerGroupUrl: "https://systemuat.auct.co.th:3443/api/v1/masterCustomer/getCustomerGroup"
  customerTypeUrl: "https://systemuat.auct.co.th:3443/api/v1/masterCustomer/getCustomerType"
  assetLocationFloorUrl: "https://systemuat.auct.co.th:3443/api/v1/masterAssetLocationFloor/getAssetLocationFloor"
  reasonUrl: "https://systemuat.auct.co.th:3443/api/v1/masterReason/getReason"
  lotSouUrl: "https://systemuat.auct.co.th:3443/api/v1/lot/getlot"
  lotSouLotLineUrl: "https://systemuat.auct.co.th:3443/api/v1/lot/getLotLine"
  vatCodeUrl: "https://systemuat.auct.co.th:3443/api/v1/masterVatCode/getVatCode"
  regionUrl: "https://systemuat.auct.co.th:3443/api/v1/masterRegion/getRegion"
  costRevenueUrl: "https://systemuat.auct.co.th:3443/api/v1/masterCostRevenue/getCostRevenue"
  vatBusinessUrl: "https://systemuat.auct.co.th:3443/api/v1/masterVatBusiness/getVatBusiness"
  vendorUrl: "https://systemuat.auct.co.th:3443/api/v1/masterVendor/getVendor"
  vendorGroupUrl: "https://systemuat.auct.co.th:3443/api/v1/masterVendorGroup/getVendorGroup"
  countryUrl: "https://systemuat.auct.co.th:3443/api/v1/masterCountry/getCountry"
  districtUrl: "https://systemuat.auct.co.th:3443/api/v1/masterAddress/getDistrict"
  subDistrictUrl: "https://systemuat.auct.co.th:3443/api/v1/masterAddress/getSubDistrict"
  prefixNameUrl: "https://systemuat.auct.co.th:3443/api/v1/masterPrefixName/getPrefixName"
  cityUrl: "https://systemuat.auct.co.th:3443/api/v1/masterAddress/getProvince"
  customerUrl: "https://systemuat.auct.co.th:3443/api/v1/masterCustomer/getCustomer"
  departmentUrl: "https://systemuat.auct.co.th:3443/api/v1/masterDepartment/getDepartment"
  postcodeUrl: "https://systemuat.auct.co.th:3443/api/v1/masterAddress/getPostcode"
  holidayUrl: "https://systemuat.auct.co.th:3443/api/v1/masterHoliday/getHoliday"
  bankUrl: "https://systemuat.auct.co.th:3443/api/v1/masterBankAccount/getBankAccount"
  paymentMethodUrl: "https://systemuat.auct.co.th:3443/api/v1/masterPaymentMethod/getPaymentMethod"
  registerTypeUrl: "https://systemuat.auct.co.th:3443/api/v1/masterRegisterType/getRegisterType"
  registerTypeCarUrl: "https://systemuat.auct.co.th:3443/api/v1/masterRegisterTypeCar/getRegisterTypeCar"
  customerSellerOfferUrl: "https://systemuat.auct.co.th:3443/api/v1/masterSellerOffer/getCustomerSellerOffer"
  sellerOfferUrl: "https://systemuat.auct.co.th:3443/api/v1/masterSellerOffer/getSellerOffer"
  brandUrl: "https://systemuat.auct.co.th:3443/api/v1/masterBrand/getBrand"
  modelUrl: "https://systemuat.auct.co.th:3443/api/v1/masterModel/getModel"
  assetLocationUrl: "https://systemuat.auct.co.th:3443/api/v1/masterAssetLocation/getAssetLocation"
