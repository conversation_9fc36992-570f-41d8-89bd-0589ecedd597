package entity

import (
	"backend-common-lib/model"
	"time"

	"gorm.io/gorm"
)

type AdditionalService struct {
	*model.BaseEntity
	ServiceName   *string                `column:"service_name" json:"serviceName"`
	ServiceTypeId *int                   `column:"service_type_id" json:"serviceTypeId"`
	Detail        *string                `column:"detail" json:"detail"`
	StartDate     *time.Time             `column:"start_date" json:"startDate"`
	EndDate       *time.Time             `column:"end_date" json:"endDate"`
	FileKey       *string                `column:"file_key" json:"fileKey"`
	FileUrl       *string                `column:"file_url" json:"fileUrl"`
	FileName      *string                `column:"file_name" json:"fileName"`
	IsActive      bool                   `column:"is_active" json:"isActive"`
	DeletedDate   *gorm.DeletedAt        `gorm:"column:deleted_date" json:"deletedDate"`
	CreatedUser   *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:UserID"`
	UpdatedUser   *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:UserID"`
	ServiceTypeTh *string                `gorm:"column:service_type_th;->" json:"serviceTypeTh"`
	ServiceTypeEn *string                `gorm:"column:service_type_en;->" json:"serviceTypeEn"`
}

func (AdditionalService) TableName() string {
	return "additional_service"
}
