package paymentduenotification

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/due_noti_config"
	repositoryExcludedBuyer "content-service/internal/repository/due_noti_config_excluded_buyer"
	service "content-service/internal/service/due_noti_config"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewDueNotiConfigRepository(db)
	repoExcludedBuyer := repositoryExcludedBuyer.NewDueNotiConfigExcludedBuyerRepository(db)
	service := service.NewDueNotiConfigService(repo, repoExcludedBuyer)
	handler := &Handler{Service: service}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("registration-book-due-notifications")
	route.Post("/search", h.GetRegistrationBookDueNotification)
	route.Get("/:id", h.GetRegistrationBookDueNotificationByID)
	route.Post("/", h.CreateRegistrationBookDueNotification)
	route.Put("/:id", h.UpdateRegistrationBookDueNotification)
	route.Put("/status/:id", h.UpdateRegistrationBookDueNotificationStatus)
	route.Delete("/:id", h.DeleteRegistrationBookDueNotification)
}
