package repository

import (
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type userBidderNumbersRepositoryImpl struct {
	DB *gorm.DB
}

type UserBidderNumbersRepository interface {
	FindUserBidderNumberByBidderNumber(bidderNumberId string, soldDate time.Time) (*entity.UserBidderNumbers, error)
	FindUserBidderNumberByBidderNumberAndLotId(bidderNumberId string, lotId int, soldDate time.Time) (*entity.UserBidderNumbers, error)
}

func NewUserBidderNumbersRepository(db *gorm.DB) UserBidderNumbersRepository {
	return &userBidderNumbersRepositoryImpl{DB: db}
}
