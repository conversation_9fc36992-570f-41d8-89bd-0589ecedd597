package dto

import (
	"backend-common-lib/model"
	"time"
)

type MasterAssetLocationDto struct {
	model.BaseDto
	LocationCode   *string `json:"locationCode"`
	DescriptionTh  *string `json:"descriptionTh"`
	DescriptionEn  *string `json:"descriptionEn"`
	BranchCode     *string `json:"branchCode"`
	IsSaleLocation bool    `json:"isSaleLocation"`
	CountryCode    *string `json:"countryCode"`
	RegionCode     *string `json:"regionCode"`
	CityCode       *string `json:"cityCode"`
	PostCode       *string `json:"postCode"`
	IsActive       bool    `json:"isActive"`
	CompanyCode    *string `json:"companyCode"`
	IsDeletedByErp bool    `json:"isDeletedByErp"`
}

type MasterAssetLocationPageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate" example:"2020-01-01"`
}

type MasterAssetLocationSyncErpRespDto struct {
	CompanyCode    *string `json:"CompanyCode"`
	LocationCode   *string `json:"Location_Code"`
	DescriptionTh  *string `json:"Description_TH"`
	DescriptionEn  *string `json:"Description_EN"`
	BranchCode     *string `json:"Branch_Code"`
	IsSaleLocation bool    `json:"Sale_Location"`
	CountryCode    *string `json:"Country_Code"`
	RegionCode     *string `json:"Region_Code"`
	CityCode       *string `json:"City_Code"`
	PostCode       *string `json:"Postcode"`
	Status         string  `json:"Status"`
}

type MasterAssetLocationPageReqDto struct {
	LocationCode  *string `json:"locationCode"`
	DescriptionTh *string `json:"descriptionTh"`
	DescriptionEn *string `json:"descriptionEn"`
	BranchCode    *string `json:"branchCode"`
	RegionCode    *string `json:"regionCode"`
	CityCode      *string `json:"cityCode"`
	IsActive      *bool   `json:"isActive"`
	model.PagingRequest
}

type MasterAssetLocationListDto struct {
	MasterAssetLocationList []MasterAssetLocationDto `json:"branchMasterList"`
}

type MasterAssetLocationUpdateReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
