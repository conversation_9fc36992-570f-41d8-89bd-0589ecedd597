package repository

import (
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

//UpdateMasterModelAllFields
//InsertMasterModelList
//FindMasterModelAll

func (r *masterModelRepositoryImpl) FindMasterModelAll() ([]entity.MasterModel, error) {
	var result []entity.MasterModel
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterModelRepositoryImpl) UpdateMasterModelAllFields(e *entity.MasterModel) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterModelRepositoryImpl) InsertMasterModelList(data []entity.MasterModel) error {
	if err := r.DB.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterModelRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
