package mastermodel

import (
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	service "content-service/internal/service/master_model"

	"net/http"

	"github.com/gofiber/fiber/v2"
)

type Handler struct {
	Service   service.MasterModelService
	ErpConfig global.ErpConfig
}

func (h *Handler) SyncMasterModelFromErp(c *fiber.Ctx) error {
	var req model.BaseDtoActionBy

	actionBy := util.GetActionByFromHeader(c)
	req.ActionBy = actionBy

	err := h.Service.SyncMasterModelFromErp(req.ActionBy, h.ErpConfig)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}
