package entity

import (
	"backend-common-lib/model"
)

type BuyerBankAccount struct {
	*model.BaseEntity
	UserId        int     `gorm:"column:user_id;not null" json:"user_id"`
	AccountNumber *string `gorm:"column:account_number;size:50" json:"account_number"`
	BankAccount   *string `gorm:"column:bank_account;size:100" json:"bank_account"`
	BankName      *string `gorm:"column:bank_name;size:100" json:"bank_name"`
	FileName      *string `gorm:"column:filename;size:100" json:"filename"`
	FileType      *string `gorm:"column:file_type;size:50" json:"file_type"`
	FileCategory  *string `gorm:"column:file_category;size:50" json:"file_category"`
	Bytes         []byte  `gorm:"column:bytes" json:"bytes"`
	IsActive      bool    `gorm:"column:is_active;default:true;not null" json:"is_active"`
	IsDelete      bool    `gorm:"column:is_delete;default:false;not null" json:"is_delete"`
}

func (BuyerBankAccount) TableName() string {
	return "buyer_bank_account"
}

type BuyerBankAccountLog struct {
	*BuyerBankAccount
	BuyerBankAccountId int    `gorm:"column:buyer_bank_account_id;not null" json:"buyer_bank_account_id"`
	Action             string `gorm:"column:action;size:20;not null" json:"action"` // INSERT, UPDATE, DELETE
}

func (BuyerBankAccountLog) TableName() string {
	return "buyer_bank_account_log"
}
