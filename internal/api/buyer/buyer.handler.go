package buyer

import (
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"content-service/internal/model/dto"
	service "content-service/internal/service/buyer"
	"net/http"

	"github.com/gofiber/fiber/v2"
)

type Handler struct {
	Service service.BuyerService
}

func (h *Handler) FindBuyerDetailsWithFilter(c *fiber.Ctx) error {
	var req dto.BuyerSearchReqDto
	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}
	data, err := h.Service.FindBuyerDetailsWithFilter(req)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&data))
}
