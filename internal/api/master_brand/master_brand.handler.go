package masterbrand

import (
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	service "content-service/internal/service/master_brand"
	"net/http"

	"github.com/gofiber/fiber/v2"
)

type Handler struct {
	Service   service.MasterBrandService
	ErpConfig global.ErpConfig
}

func (h *Handler) SyncMasterBrandFromErp(c *fiber.Ctx) error {
	var req model.BaseDtoActionBy

	actionBy := util.GetActionByFromHeader(c)
	req.ActionBy = actionBy

	err := h.Service.SyncMasterBrandFromErp(req.ActionBy, h.ErpConfig)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}
