package service

import (
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/registration_buyer_form_config"
)

type registrationBuyerFormConfigService struct {
	Repo repository.RegistrationBuyerFormConfigRepository
}

type RegistrationBuyerFormConfigService interface {
	GetRegistrationBuyerFormConfig() (dto.RegistrationBuyerFormConfigRespDto, error)
	UpdateRegistrationBuyerFormConfig(req dto.RegistrationBuyerFormConfigUpdateReqDto) error
}

func NewRegistrationBuyerFormConfigService(repo repository.RegistrationBuyerFormConfigRepository) RegistrationBuyerFormConfigService {
	return &registrationBuyerFormConfigService{Repo: repo}
}
