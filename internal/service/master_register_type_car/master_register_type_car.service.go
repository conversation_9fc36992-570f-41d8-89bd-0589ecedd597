package service

import (
	"content-service/internal/global"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/master_register_type_car"
)

type masterRegisterTypeCarService struct {
	Repo repository.MasterRegisterTypeCarRepository
}

type MasterRegisterTypeCarService interface {
	SearchMasterRegisterTypeCarFilter(req dto.MasterRegisterTypeCarPageReqDto) (dto.MasterRegisterTypeCarPageRespDto[dto.MasterRegisterTypeCarDto], error)
	UpdateMasterRegisterTypeCarStatus(req dto.MasterRegisterTypeCarUpdateReqDto) error
	SyncMasterRegisterTypeCarFromErp(actionBy *int, erpConfig global.ErpConfig) error
}

func NewMasterRegisterTypeCarService(repo repository.MasterRegisterTypeCarRepository) MasterRegisterTypeCarService {
	return &masterRegisterTypeCarService{Repo: repo}
}
