package service

import (
	"net/http"

	"backend-common-lib/erp"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_model"

	"gorm.io/gorm"
)

func (s *masterModelService) SyncMasterModelFromErp(actionBy *int, erpConfig global.ErpConfig) error {
	erpMap, allKeys, err := s.getModelFromErp(erpConfig)
	if err != nil {
		return err
	}
	dbMap, err := s.getModelFromDb(allKeys)
	if err != nil {
		return err
	}
	err = s.syncModel(actionBy, erpMap, dbMap, allKeys)
	if err != nil {
		return err
	}

	return nil
}

func (s *masterModelService) getModelFromErp(erpConfig global.ErpConfig) (map[string]dto.MasterModelDto, map[string]struct{}, error) {
	erpList, err := erp.FetchListFromErp[dto.MasterModelSyncErpRespDto](
		erpConfig.ModelUrl,
		erpConfig.LoginUrl,
		erpConfig.Email,
		erpConfig.Password,
		map[string]interface{}{"Company_Name": "AUCT"},
		"data",
	)
	if err != nil {
		return nil, nil, errs.NewError(http.StatusInternalServerError, err)
	}

	erpMap := make(map[string]dto.MasterModelDto)
	allKeys := make(map[string]struct{})

	for _, e := range erpList {
		erpMap[util.Val(e.ModelCode)] = dto.MasterModelDto{
			CompanyCode:   e.CompanyCode,
			AssetTypeCode: e.AssetTypeCode,
			IsActive:      e.Status == "Active",
			ModelCode:     e.ModelCode,
			Description:   e.Description,
			BrandCode:     e.BrandCode,
		}
		allKeys[util.Val(e.ModelCode)] = struct{}{}
	}
	return erpMap, allKeys, nil
}

func (s *masterModelService) getModelFromDb(allKeys map[string]struct{}) (map[string]entity.MasterModel, error) {
	dbList, err := s.Repo.FindMasterModelAll()
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	dbMap := make(map[string]entity.MasterModel)
	for _, e := range dbList {
		dbMap[util.Val(e.ModelCode)] = e
		allKeys[util.Val(e.ModelCode)] = struct{}{}
	}
	return dbMap, nil
}

func (s *masterModelService) syncModel(actionBy *int, erpMap map[string]dto.MasterModelDto, dbMap map[string]entity.MasterModel, allKeys map[string]struct{}) error {
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		repo := repository.NewMasterModelRepository(tx)
		var toInsert []entity.MasterModel
		currentDateTime := util.NowPtr()

		for code := range allKeys {
			erp, erpExists := erpMap[code]
			db, dbExists := dbMap[code]

			if erpExists && dbExists {
				// มีทั้ง ERP + DB → compare → update หมด
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.CompanyCode = erp.CompanyCode
				temp.AssetTypeCode = erp.AssetTypeCode
				temp.ModelCode = erp.ModelCode
				temp.BrandCode = erp.BrandCode
				temp.Description = erp.Description
				temp.IsActive = erp.IsActive
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterModelAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}

			} else if erpExists && !dbExists {
				// มีใน ERP แต่ไม่มีใน DB → insert
				temp := entity.MasterModel{
					BaseEntity: &model.BaseEntity{
						CreatedDate: *currentDateTime,
						CreatedBy:   actionBy,
						UpdatedDate: currentDateTime,
						UpdatedBy:   actionBy,
					},
					CompanyCode:    erp.CompanyCode,
					AssetTypeCode:  erp.AssetTypeCode,
					BrandCode:      erp.BrandCode,
					ModelCode:      erp.ModelCode,
					Description:    erp.Description,
					IsActive:       erp.IsActive,
					IsDeletedByErp: false,
					LatestSyncDate: currentDateTime,
				}
				toInsert = append(toInsert, temp)

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterModelAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}
		}

		if len(toInsert) > 0 {
			if err := repo.InsertMasterModelList(toInsert); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}
		return nil
	})
}
