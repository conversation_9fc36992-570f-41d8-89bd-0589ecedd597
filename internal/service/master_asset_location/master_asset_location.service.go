package service

import (
	"content-service/internal/global"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/master_asset_location"
)

type masterAssetLocationService struct {
	Repo repository.MasterAssetLocationRepository
}

type MasterAssetLocationService interface {
	SearchMasterAssetLocationFilter(req dto.MasterAssetLocationPageReqDto) (dto.MasterAssetLocationPageRespDto[dto.MasterAssetLocationDto], error)
	UpdateMasterAssetLocationStatus(req dto.MasterAssetLocationUpdateReqDto) error
	SyncMasterAssetLocationFromErp(actionBy *int, erpConfig global.ErpConfig) error
}

func NewMasterAssetLocationService(repo repository.MasterAssetLocationRepository) MasterAssetLocationService {
	return &masterAssetLocationService{Repo: repo}
}
