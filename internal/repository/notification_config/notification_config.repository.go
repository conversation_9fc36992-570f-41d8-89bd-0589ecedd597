package repository

import (
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type notificationConfigRepositoryImpl struct {
	DB *gorm.DB
}

type NotificationConfigRepository interface {
	FindAllNotificationConfigByUserType(userType *string) ([]entity.NotificationConfig, error)

	UpdatesNotificationConfigFieldsWhere(tx *gorm.DB, fields map[string]interface{}, whereClause string, args ...interface{}) (int64, *entity.NotificationConfig, error)
	GetDB() *gorm.DB
}

func NewNotificationConfigRepository(db *gorm.DB) NotificationConfigRepository {
	return &notificationConfigRepositoryImpl{DB: db}
}
