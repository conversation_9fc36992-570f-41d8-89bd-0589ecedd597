package repository

import (
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

func (r *buyerEditProfileRequestPastRepositoryImpl) GetByBuyerEditProfileRequestCurrrentId(buyerEditProfileRequestCurrentId int) (entity.BuyerEditProfileRequestPast, error) {
	var buyerEditProfileRequestPast entity.BuyerEditProfileRequestPast
	err := r.DB.Table("buyer_edit_profile_request_past").Select(
		`buyer_edit_profile_request_past.*,
		pa.description_th AS prefix_name_th,
		pa.description_en AS prefix_name_en,
		sd.description_th AS sub_district_description_th,
		sd.description_en AS sub_district_description_en,
		d.description_th AS district_description_th,
		d.description_en AS district_description_en,
		p.description_th AS province_description_th,
		p.description_en AS province_description_en,
		c.description_th AS country_description_th,
		c.description_en AS country_description_en`).
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_prefix_name) AS pa ON pa.id = buyer_edit_profile_request_past.prefix_name_id").
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_sub_district) AS sd ON sd.id = buyer_edit_profile_request_past.sub_district_id").
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_district) AS d ON d.id = buyer_edit_profile_request_past.district_id").
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_city) AS p ON p.id = buyer_edit_profile_request_past.province_id").
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_country) AS c ON c.id = buyer_edit_profile_request_past.country_id").
		Where("buyer_edit_profile_request_past.buyer_edit_profile_request_current_id = ?", buyerEditProfileRequestCurrentId).First(&buyerEditProfileRequestPast).Error
	return buyerEditProfileRequestPast, err
}

func (r *buyerEditProfileRequestPastRepositoryImpl) Insert(entity entity.BuyerEditProfileRequestPast) error {
	err := r.DB.Create(&entity).Error
	return err
}

func (r *buyerEditProfileRequestPastRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
