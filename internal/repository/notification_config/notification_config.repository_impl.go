package repository

import (
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

func (r *notificationConfigRepositoryImpl) buildNotificationConfigQuery(userType *string) *gorm.DB {
	query := r.DB.Model(&entity.NotificationConfig{}).Where("user_type = ?", userType)
	return query
}

func (r *notificationConfigRepositoryImpl) FindAllNotificationConfigByUserType(userType *string) ([]entity.NotificationConfig, error) {
	var results []entity.NotificationConfig

	query := r.buildNotificationConfigQuery(userType)
	query.Order("id asc")

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *notificationConfigRepositoryImpl) UpdatesNotificationConfigFieldsWhere(tx *gorm.DB, fields map[string]interface{}, whereClause string, args ...interface{}) (int64, *entity.NotificationConfig, error) {
	var updatedRecord entity.NotificationConfig
	result := tx.Model(&entity.NotificationConfig{}).Where(whereClause, args...).Updates(fields).Scan(&updatedRecord)
	if result.Error != nil {
		return 0, nil, result.Error
	}
	return result.RowsAffected, &updatedRecord, nil
}

func (r *notificationConfigRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
