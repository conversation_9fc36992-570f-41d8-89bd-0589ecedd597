package repository

import (
	"backend-common-lib/constant"
	"backend-common-lib/model"
	"backend-common-lib/util"
	contentConst "content-service/constant"
	"content-service/internal/model/entity"
	"fmt"

	"gorm.io/gorm"
)

func (r *dueNotiConfigRepositoryImpl) buildDueNotiConfigQuery(notiType string) *gorm.DB {
	query := r.DB.Model(&entity.DueNotiConfig{}).
		Select(`due_noti_config.*, 
		customer_group.description_th AS customer_group_desc,
		cfg.value_string AS days_before_due_en,
		cfg.value_string2 AS days_before_due_th`)
	query = util.JoinUsers("due_noti_config")(query)
	query = query.
		Joins("LEFT JOIN master_customer_group AS customer_group ON customer_group.id = due_noti_config.customer_group_id").
		Joins(`LEFT JOIN config_parameters AS cfg ON 
		cfg.parameter_module = ? AND 
		cfg.parameter_name = ? AND 
		cfg.value_int = due_noti_config.days_before_due`,
			constant.ConfigParamConst.MODULE_SYSTEM_ADMIN,
			constant.ConfigParamConst.DAYS_BEFORE_DUE,
		)
	if notiType != "" {
		query = query.
			Where("due_noti_config.noti_type = ?", notiType)
	}
	return query
}

func (r *dueNotiConfigRepositoryImpl) FindAllDueNotiConfig(req model.PagingRequest, notiType string) ([]entity.DueNotiConfig, error) {
	var results []entity.DueNotiConfig

	query := r.buildDueNotiConfigQuery(notiType)
	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplySortFromMapField(query, snakeSort, req.SortOrder, "due_noti_config", contentConst.SortingDueNotiConfig, true)
	}

	//NOTE - default order
	query.Order(fmt.Sprintf("%s %s", "customer_group.description_th", "asc"))

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *dueNotiConfigRepositoryImpl) CountAllDueNotiConfig(notiType string) (int64, error) {
	var count int64
	query := r.buildDueNotiConfigQuery(notiType)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *dueNotiConfigRepositoryImpl) FindDueNotiConfigByID(id int) (*entity.DueNotiConfig, error) {
	var result entity.DueNotiConfig

	query := r.buildDueNotiConfigQuery("")
	query = query.Where("due_noti_config.id = ?", id)

	if err := query.First(&result).Error; err != nil {
		return nil, err
	}

	return &result, nil
}

func (r *dueNotiConfigRepositoryImpl) InsertDueNotiConfig(entityPaymentDue *entity.DueNotiConfig) error {
	if err := r.DB.Create(entityPaymentDue).Error; err != nil {
		return err
	}
	return nil
}

func (r *dueNotiConfigRepositoryImpl) FindByCustomerGroupId(customerGroupId int, notiType string) ([]entity.DueNotiConfig, error) {
	var results []entity.DueNotiConfig

	query := r.DB.Where("customer_group_id = ? and noti_type = ?", customerGroupId, notiType)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *dueNotiConfigRepositoryImpl) UpdateDueNotiConfig(id int, fieldToUpdate map[string]interface{}) (int64, error) {
	result := r.DB.Model(&entity.DueNotiConfig{}).
		Where("id = ?", id).
		Updates(fieldToUpdate)
	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}

func (r *dueNotiConfigRepositoryImpl) DeleteDueNotiConfig(id int) (int64, error) {
	result := r.DB.Where("id = ?", id).Delete(&entity.DueNotiConfig{})

	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}

func (r *dueNotiConfigRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
