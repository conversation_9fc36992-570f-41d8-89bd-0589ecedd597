package entity

import (
	"time"

	"gorm.io/gorm"
)

type BuyerEditProfileRequestPast struct {
	Id                               int             `gorm:"primaryKey" column:"id" json:"id" ignore:"true"`
	BuyerEditProfileRequestCurrentId *int            `column:"buyer_edit_profile_request_current_id" json:"buyerEditProfileRequestCurrentId"`
	BidderId                         *string         `column:"bidder_id" json:"bidderId"`
	Username                         *string         `column:"username" json:"username"`
	Email                            *string         `column:"email" json:"email"`
	PhoneNumber                      *string         `column:"phone_number" json:"phoneNumber"`
	TaxId                            *string         `column:"tax_id" json:"taxId"`
	IdentificationNumber             *string         `column:"identification_number" json:"identificationNumber"`
	NationalityId                    *int            `column:"nationality_id" json:"nationalityId"`
	DateOfBirth                      *time.Time      `column:"date_of_birth" json:"dateOfBirth"`
	PrefixNameId                     *int            `column:"prefix_name_id" json:"prefixNameId"`
	FirstName                        *string         `column:"first_name" json:"firstName"`
	MiddleName                       *string         `column:"middle_name" json:"middleName"`
	LastName                         *string         `column:"last_name" json:"lastName"`
	HouseNumber                      *string         `column:"house_number" json:"houseNumber"`
	RoomNumber                       *string         `column:"room_number" json:"roomNumber"`
	Floor                            *string         `column:"floor" json:"floor"`
	Building                         *string         `column:"building" json:"building"`
	Village                          *string         `column:"village" json:"village"`
	Moo                              *string         `column:"moo" json:"moo"`
	Soi                              *string         `column:"soi" json:"soi"`
	Road                             *string         `column:"road" json:"road"`
	PostCode                         *string         `column:"post_code" json:"postCode"`
	SubDistrictId                    *int            `column:"sub_district_id" json:"subDistrictId"`
	DistrictId                       *int            `column:"district_id" json:"districtId"`
	ProvinceId                       *int            `column:"province_id" json:"provinceId"`
	CountryId                        *int            `column:"country_id" json:"countryId"`
	DeletedDate                      *gorm.DeletedAt `gorm:"column:deleted_date" json:"deletedDate"`
	PrefixNameTh                     *string         `gorm:"column:prefix_name_th;->" json:"prefixNameTh"`
	PrefixNameEn                     *string         `gorm:"column:prefix_name_en;->" json:"prefixNameEn"`
	NationalityTh                    *string         `gorm:"column:nationality_th;->" json:"nationalityTh"`
	NationalityEn                    *string         `gorm:"column:nationality_en;->" json:"nationalityEn"`
	SubDistrictDescriptionTh         *string         `gorm:"column:sub_district_description_th;->" json:"subDistrictDescriptionTh"`
	SubDistrictDescriptionEn         *string         `gorm:"column:sub_district_description_en;->" json:"subDistrictDescriptionEn"`
	DistrictDescriptionTh            *string         `gorm:"column:district_description_th;->" json:"districtDescriptionTh"`
	DistrictDescriptionEn            *string         `gorm:"column:district_description_en;->" json:"districtDescriptionEn"`
	ProvinceDescriptionTh            *string         `gorm:"column:province_description_th;->" json:"provinceDescriptionTh"`
	ProvinceDescriptionEn            *string         `gorm:"column:province_description_en;->" json:"provinceDescriptionEn"`
	CountryDescriptionTh             *string         `gorm:"column:country_description_th;->" json:"countryDescriptionTh"`
	CountryDescriptionEn             *string         `gorm:"column:country_description_en;->" json:"countryDescriptionEn"`
	BuyerEditProfileRequestId        *int            `gorm:"column:buyer_edit_profile_request_id;->" json:"buyerEditProfileRequestId"`
}

func (BuyerEditProfileRequestPast) TableName() string {
	return "buyer_edit_profile_request_past"
}
