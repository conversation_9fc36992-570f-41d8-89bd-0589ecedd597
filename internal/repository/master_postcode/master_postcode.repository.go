package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type masterPostcodeRepositoryImpl struct {
	DB *gorm.DB
}

type MasterPostcodeRepository interface {
	FindMasterPostcodeWithFilter(req dto.MasterPostcodePageReqDto) ([]entity.MasterPostcode, error)
	CountMasterPostcodeWithFilter(req dto.MasterPostcodePageReqDto) (int64, error)
	FindLatestSyncDate() (*time.Time, error)

	UpdatesMasterPostcodeFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)

	FindMasterPostcodeAll() ([]entity.MasterPostcode, error)
	UpdateMasterPostcodeAllFields(e *entity.MasterPostcode) error
	InsertMasterPostcodeListWithBatches(data []entity.MasterPostcode) error
	GetBySubDistrictID(subDistrictID int) (*entity.MasterPostcode, error)

	GetDB() *gorm.DB
}

func NewMasterPostcodeRepository(db *gorm.DB) MasterPostcodeRepository {
	return &masterPostcodeRepositoryImpl{DB: db}
}
