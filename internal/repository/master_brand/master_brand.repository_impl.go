package repository

import (
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

//UpdateMasterBrandAllFields
//InsertMasterBrandList
//FindMasterBrandAll

func (r *masterBrandRepositoryImpl) FindMasterBrandAll() ([]entity.MasterBrand, error) {
	var result []entity.MasterBrand
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterBrandRepositoryImpl) UpdateMasterBrandAllFields(e *entity.MasterBrand) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterBrandRepositoryImpl) InsertMasterBrandList(data []entity.MasterBrand) error {
	if err := r.DB.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterBrandRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
