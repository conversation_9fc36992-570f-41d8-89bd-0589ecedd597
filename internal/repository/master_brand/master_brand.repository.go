package repository

import (
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type masterBrandRepositoryImpl struct {
	DB *gorm.DB
}

type MasterBrandRepository interface {
	FindMasterBrandAll() ([]entity.MasterBrand, error)
	UpdateMasterBrandAllFields(e *entity.MasterBrand) error
	InsertMasterBrandList(data []entity.MasterBrand) error
	GetDB() *gorm.DB
}

func NewMasterBrandRepository(db *gorm.DB) MasterBrandRepository {
	return &masterBrandRepositoryImpl{DB: db}
}
